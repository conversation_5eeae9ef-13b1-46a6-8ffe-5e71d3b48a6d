<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\Controller;
use App\Models\BlogCategories;
use App\Models\Blogs;

class websiteController extends Controller
{
    public function HomeView()
    {
    //   $articles = Blogs::where('status', 1)
    //         ->where('type', 'blog')
    //         ->orderBy('created_at', 'desc')
    //         ->take(3)
    //         ->get();
      

        return view('website.index');
    }
     public function AboutView()
    {
    //   $articles = Blogs::where('status', 1)
    //         ->where('type', 'blog')
    //         ->orderBy('created_at', 'desc')
    //         ->take(3)
    //         ->get();
      

        return view('website.about');
    }
    public function blogView()
    {
        $categories = BlogCategories::get();

        $articles = Blogs::where('status', 1)
            ->where('type', 'blog')->orderBy('created_at', 'desc')
            ->paginate(8);

        $latestArticles = Blogs::where('status', 1)->where('popular', 1)
            ->where('type', 'blog')
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->get();
        return view('website.blog', compact('categories', 'articles', 'latestArticles'));
    }

    public function singleBlogView($slug)
    {
        $singleArticle = Blogs::where('slug', $slug)->where('status', 1)->first();
        if (!$singleArticle) {
            // If the article is not found, return a 404 error
            abort(404);
        }

        // Get related articles for sidebar
        $latestArticles = Blogs::where('status', 1)->where('popular', 1)
            ->where('type', 'blog')
            ->where('id', '!=', $singleArticle->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return view('website.blogDetail', compact('singleArticle', 'latestArticles'));
    }
}
