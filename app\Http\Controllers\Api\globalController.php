<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Businesses;
use App\Models\States;
use App\Models\Franchisees;
use Illuminate\Support\Carbon;

class globalController extends Controller
{

    public function galleryView()
    {
        $user = Auth::user();
        $business = Businesses::first();
        // Retrieve and decode the existing media field
        // $media = $business->media;
        // $imageUrls = json_decode($media, true) ?: [];

        // Retrieve and decode `user_media`
        $userMedia = json_decode($business->user_media, true) ?? [];

        // Get the images for the logged-in user
        $imageUrls = $userMedia[$user->id] ?? [];

        return response()->json($imageUrls);
    }


    public function uploader(request $request)
    {
        $user = Auth::user();

        $business = Businesses::first();

        if (!$business) {
            return response()->json(['error' => 'Business not found.'], 404);
        }


        $validator = Validator::make($request->all(), [
            'web_images' => 'required|mimes:jpeg,png,jpg,webp,pdf,doc,docx,csv,excel,mp4|max:4048',
        ]);


        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Handle image upload
        if ($request->hasFile('web_images')) {
            $file = $request->file('web_images');
            $webImagePath = media($file, 'storage/web');
            $webImagePath = basename($webImagePath);

            $url = asset('storage/web/' . $webImagePath);

            ////////////////////////////////////////////////////////////////////////////////////
            // Retrieve and decode the existing media field
            $media = $business->media;
            $mediaArray = json_decode($media, true) ?: [];
            // Append the new image URL
            $mediaArray[] = $url;

            // Encode the array back to JSON and save it
            $business->media = json_encode($mediaArray);
            $business->save();
            ////////////////////////////////////////////////////////////////////////////////////

            ////////////////////////////////////////////////////////////////////////////////////
            // Retrieve and decode the existing `user_media` field
            $userMedia = json_decode($business->user_media, true) ?? [];

            // Check if the logged-in user's ID exists in `user_media`
            if (!isset($userMedia[$user->id])) {
                $userMedia[$user->id] = []; // Initialize user media array if not present
            }

            // Append the new image URL to the user's media array
            $userMedia[$user->id][] = $url;

            // Save updated data back to the database
            $business->user_media = json_encode($userMedia);
            $business->save();
            ////////////////////////////////////////////////////////////////////////////////////

            // Return the URL in JSON format
            return response()->json(['location' => $url]);
        }

        return response()->json(['error' => 'No file uploaded.'], 400);
    }

    public function deleteFile(request $request)
    {
        $user = Auth::user();
        $business = Businesses::first();

        if (!$business) {
            return response()->json(['error' => 'Business not found.'], 404);
        }
        $imageToDelete = $request->input('image');

        // Decode the media JSON field
        $mediaArray = json_decode($business->media, true) ?: [];
        $userMediaArray = json_decode($business->user_media, true) ?: [];

        // Check if the image exists in media
        $imageFoundInMedia = in_array($imageToDelete, $mediaArray);

        // Check if the image exists in user_media for the logged-in user
        $imageFoundInUserMedia = isset($userMediaArray[$user->id]) && in_array($imageToDelete, $userMediaArray[$user->id]);

        if (!$imageFoundInMedia && !$imageFoundInUserMedia) {
            return response()->json(['message' => 'Image not found in records.'], 404);
        }

        // Remove the image from the array
        $updatedMediaArray = array_filter($mediaArray, function ($image) use ($imageToDelete) {
            return $image !== $imageToDelete;
        });

        // Remove from user_media for the logged-in user
        if (isset($userMediaArray[$user->id])) {
            $userMediaArray[$user->id] = array_filter($userMediaArray[$user->id], function ($image) use ($imageToDelete) {
                return $image !== $imageToDelete;
            });

            // If the user's media is empty, remove the key
            if (empty($userMediaArray[$user->id])) {
                unset($userMediaArray[$user->id]);
            }
        }
        // Extract the file path from the full URL
        $imagePath = parse_url($imageToDelete, PHP_URL_PATH); // This will give you "/storage/web/filename.webp"

        // Remove the "/storage/" part and get the relative path inside "web" directory
        $fileName = str_replace('/storage/', '', $imagePath); // This will give "web/filename.webp"

        $publicPath = 'storage/' . $fileName; // For "public/storage/web/"

        // Check if the file exists in "public/storage/web/"
        if (file_exists(public_path($publicPath))) {
            unlink(public_path($publicPath)); // Delete from "public/storage/web/"
        }

        // Save the updated media back to the database
        $business->media = json_encode(array_values($updatedMediaArray)); // Re-index the array
        $business->user_media = json_encode($userMediaArray);
        $business->save();

        return response()->json(['success' => true, 'message' => 'Image deleted successfully.']);
    }

    public function webSettings(request $request)
    {
        $business = Businesses::first();

        if (!$business) {
            return response()->json(['error' => 'Business not found.'], 404);
        }

        $website_logo = [
            'logo' => $request->website_logo,
            'favicon' => $request->favicon,
        ];


        $business->contact_email = $request->contact_email;
        $business->newsletter_email = $request->newsletter_email;


        $business->business_logo = json_encode($website_logo);

        $business->save();

        return response()->json(["message" => "updated !", "status" => "success"], 200);
    }


    public function getFilterStates($country_id)
    {
        $states = States::where('country_id', $country_id)->get();
        return response()->json($states);
    }


    public function leaderboardFilter(Request $request)
    {
        $startDate = null;
        $endDate = null;

        // Parse date range
        if ($request->date_range) {
            $dates = explode(' - ', $request->date_range);
            $startDate = Carbon::createFromFormat('d M Y', trim($dates[0]))->startOfDay();
            $endDate = Carbon::createFromFormat('d M Y', trim($dates[1]))->endOfDay();
        }

        // Return early if no filters
        if (!$request->country && !$request->state && !$startDate && !$endDate) {
            return response()->json(['html' => '<div class="alert alert-warning">Please select at least one filter to proceed.</div>']);
        }

        if ($request->date_range) {
            // Base query
            $franchiseesQuery = Franchisees::with(['country', 'state'])
                ->when($request->country, fn($q) => $q->where('country_id', $request->country))
                ->when($request->state, fn($q) => $q->where('state_id', $request->state))
                ->withCount([
                    'projects as completed_projects_count' // now counts ALL projects
                ])
                ->withSum([
                    'projects as completed_projects_revenue'
                ], 'project_value')->whereBetween('created_at', [$startDate, $endDate]);
        } else {

            // Base query
            $franchiseesQuery = Franchisees::with(['country', 'state'])
                ->when($request->country, fn($q) => $q->where('country_id', $request->country))
                ->when($request->state, fn($q) => $q->where('state_id', $request->state))
                ->withCount([
                    'projects as completed_projects_count' // now counts ALL projects
                ])
                ->withSum([
                    'projects as completed_projects_revenue'
                ], 'project_value');
           
        }
        $franchisees = $franchiseesQuery->get();

        // Group by Country > State and mark top seller
        $grouped = $franchisees->groupBy(fn($f) => optional($f->country)->name ?? 'Unknown Country')
            ->map(function ($countryGroup) {
                return $countryGroup->groupBy(fn($f) => optional($f->state)->name ?? 'Unknown State')
                    ->map(function ($stateGroup) {
                        $sorted = $stateGroup->sortByDesc('completed_projects_count')->values();
                        if ($sorted->isNotEmpty()) {
                            $sorted[0]->is_top_seller = true;
                        }
                        return $sorted;
                    });
            });

        // Top sellers country-wise
        $topCountryFranchisees = $franchisees
            ->sortByDesc('completed_projects_count')
            ->groupBy(fn($f) => optional($f->country)->name ?? 'Unknown Country');

        // Render view or show no-records message
        $html = $franchisees->isEmpty()
            ? '<div class="alert alert-warning text-center mt-4">No records found for the selected filters.</div>'
            : view('dashboard.leaderBoard.partials.leaderboard', [
                'filterGroupedFranchisees' => $grouped,
                'filterTopCountryFranchisees' => $topCountryFranchisees,
            ])->render();

        return response()->json(['html' => $html]);
    }
}
