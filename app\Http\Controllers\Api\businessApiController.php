<?php
namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Hash;


class businessApiController extends Controller
{

    public function addBusinessUser(Request $request)
    {
        $user = Auth::user();

        
        $existingUser = User::where('email', $request->email)->first();

        if ($existingUser) {
            return response()->json(['message' => 'A user with this email already exists for this business', 'status' => 'error'], 422);
        }
     

        // Proceed with creating the new user
        $newUser = new User();
        $newUser->role_id = $request->role;
        $newUser->name = $request->name;
        $newUser->email = $request->email;
        $newUser->status = 0;
        //$newUser->password = Hash::make($request->password); // Ensure password is hashed
        // Encrypt the password before saving
        $newUser->password = Hash::make($request->password);
        
        $newUser->save();

        return response()->json(["message" => "business user added", "status" => "success"], 200);
    }

    public function deleteBusinessUser($id)
    {
        $user = User::findOrFail($id);
        $user->delete();
        return response()->json(["message" => "user deleted", "status" => "success"], 200);
    }

    public function businessUserStatus($id)
    {
        $user = User::findOrFail($id);
        $user->status = !$user->status;
        $user->save();

        return response()->json(["message" => "status updated", "status" => "success"], 200);
    }
    public function businessUserUpdate(Request $request)
    {
        $user = Auth::user();
  
        // Validate the input
        $validatedData = $request->validate([
         //   'id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
         //   'email' => 'required|email|max:255',
         //   'role' => 'nullable|exists:roles,id',
            'password' => 'nullable|string', // Ensures password confirmation
        ]);
    
        $businessUser = User::findOrFail($request->business_user_id);
      
        // Check for duplicate email in the same business, excluding the current user
        // $existingUser = User::where('business_id', $user->business_id)
        //     ->where('email', $validatedData['email'])
        //     ->where('id', '<>', $businessUser->id)
        //     ->first();
    
        // if ($existingUser) {
        //     return response()->json([
        //         'message' => 'A user with this email already exists for this business.',
        //         'status' => 'error'
        //     ], 422);
        // }
    
        // Update the user details
         $businessUser->name = $validatedData['name'];
        // $businessUser->email = $validatedData['email'];
    
        // Update role if provided
        // if (!empty($validatedData['role'])) {
        //     $businessUser->role_id = $validatedData['role'];
        // }
    
        // Update password if provided
        if (!empty($validatedData['password'])) {
        
            $businessUser->password = Hash::make($request->input('password'));
        }
    
        // Save the updated user details
        $businessUser->save();
    
        return response()->json([
            'message' => 'Business user updated successfully.',
            'status' => 'success',
        ], 200);
    }
    
}
