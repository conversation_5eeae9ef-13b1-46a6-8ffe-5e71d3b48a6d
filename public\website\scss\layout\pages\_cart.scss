@use '../../utils' as *;

/*----------------------------------------*/
/*  7.4 cart css start
/*----------------------------------------*/

.tp-cart{
    &-area{
        @media #{$md,$xs}{
            padding-top: 150px;
        }
    }
    &-header{
        &-product{
            padding-left: 30px !important;
        }
    }
    &-list{
        @media #{$lg}{
            overflow-x: scroll;
            margin-right: 0;
        }
        @media #{$md, $sm, $xs}{
            overflow-x: scroll;
            margin-right: 0;
        }
        .table > :not(caption) > * > * {
            padding: 20px 0;
            box-shadow: none;
            vertical-align: middle;
        }

        & table{
            @media #{$lg, $md, $sm, $xs}{
                width: 950px;
            }
        }

        & thead{
            background-color: #f5f5f5;
            & th{
                font-weight: 500;
                font-size: 18px;
                color: var(--tp-common-black);
                font-family: var(--tp-ff-marcellus);
                border: 0 !important;
                padding-top: 9px !important;
                padding-bottom: 9px !important;
            }
        }
        & tr{
            & td{
                border-color: #E0E2E3;

                & .tp-cart-action-btn{
                    font-weight: 400;
                    font-size: 14px;
                    color: #818487;
                    font-family: var(--tp-ff-marcellus);
                    & svg{
                       transform: translateY(-1px);
                    }

                    &:hover{
                        color: #FF1826;
                    }
                }

                &.tp-cart-add-to-cart{
                    & .tp-btn{
                        border-radius: 0;
                    }
                }
            }
        }

        & tbody{
            & tr{
                &:first-child{
                    & td{
                        padding-top: 30px !important;
                    }
                }
            }
        }
    }
    &-img{
        width: 78px;
        margin-right: 20px;
        & img{
            width: 78px;
            height: 100px;
            object-fit: cover;
        }
    }
    &-title{
        & a{
            margin-left: 20px;
            font-weight: 400;
            font-size: 16px;
            display: inline-block;
            color: var(--tp-common-black);
            font-family: var(--tp-ff-marcellus);
        }
    }
    &-price{
        width: 126px;
        & span{
            font-size: 16px;
            color: var(--tp-common-black);
            font-family: var(--tp-ff-marcellus);
        }
    }
    &-quantity{
        width: 180px;
    }
    &-coupon{
        @media #{$sm, $xs}{
            margin-bottom: 20px;
        }
        &-input{
            &-box{
                & label{
                    font-size: 14px;
                    margin-bottom: 7px;
                    color: var(--tp-common-black);
                    font-family: var(--tp-ff-marcellus);
                }
            }
            & input{
                background: #FFFFFF;
                border: 1px solid #D6D9DC;
                height: 46px;
                line-height: 46px;
                margin-right: 4px;
                max-width: 282px;
                font-family: var(--tp-ff-marcellus);
                @include placeholder{
                    color: #818487;
                    font-family: var(--tp-ff-marcellus);
                }
            }

            & button{
                font-weight: 500;
                font-size: 16px;
                color: var(--tp-common-white);
                background-color: var(--tp-common-black);
                font-family: var(--tp-ff-marcellus);
                padding: 9px 30px;
                border: 2px solid transparent;
                &:hover{
                    background-color: transparent;
                    color: var(--tp-common-black);
                    border-color: var(--tp-common-black);
                }
            }
        }
    }
    &-update{
       
        &-btn{
            font-weight: 500;
            font-size: 16px;
            padding: 9px 29px;
            color: var(--tp-common-black);
            background-color: var(--tp-common-white);
            border: 1px solid rgba($color: $black, $alpha: .1);
            font-family: var(--tp-ff-marcellus);

            &:hover{
                background-color: var(--tp-common-black);
                border-color: var(--tp-common-black);
                color: var(--tp-common-white);
            }
        }
    }
    &-checkout{
        &-wrapper{
            background: #f5f5f5;
            padding:  36px 24px 28px;
            margin-left: 5px;
            @media #{$md, $sm, $xs}{
                margin-top: 50px;
                margin-left: 0;
            }
        }
        &-top{
            padding-bottom: 13px;
            margin-bottom: 19px;
            border-bottom: 1px solid #E0E2E3;
            & span{
                font-size: 20px;
                font-weight: 500;
                color: var(--tp-common-black);
                font-family: var(--tp-ff-marcellus);
            }
        }
        &-shipping{
            padding-bottom: 16px;
            border-bottom: 1px solid #E0E2E3;
            margin-bottom: 15px;
            &-title{
                font-weight: 500;
                font-size: 15px;
                margin-bottom: 7px;
                font-family: var(--tp-ff-marcellus);
            }
            &-option{
                &:not(:last-child){
                    margin-bottom: 4px;
                }
                & input{
                    display: none;

                    &:checked{
                        & + label{
                            &::after{
                                border-color: var(--tp-common-black);
                            }
                            &::before{
                                opacity: 1;
                                visibility: visible;
                            }
                        }
                    }
                }
                
                & label{
                    font-weight: 400;
                    font-size: 14px;
                    color: #161C2D;
                    position: relative;
                    padding-left: 25px;
                    font-family: var(--tp-ff-marcellus);
                    &:hover{
                        cursor: pointer;
                    }
                    & span{
                        color: var(--tp-common-black);
                    }

                    &::after{
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 5px;
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        border: 1px solid #BCBCBC;
                        transition: .3s;
                    }
                    &::before{
                        position: absolute;
                        content: '';
                        left: 4px;
                        top: 9px;
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background-color: var(--tp-common-black);
                        visibility: hidden;
                        opacity: 0;
                        transition: .3s;
                    }
                }
            }
        }
        &-total{
            margin-bottom: 25px;
            & span{
                font-weight: 500;
                font-size: 18px;
                color: var(--tp-common-black);
                font-family: var(--tp-ff-marcellus);
            }
        }
        &-btn{
            font-size: 16px;
            font-weight: 500;
            color: var(--tp-common-white);
            font-family: var(--tp-ff-marcellus);
            background-color: var(--tp-common-black);
            display: inline-block;
            padding: 10px 30px;
            text-align: center;
            border: 2px solid transparent;
            &:hover{
                background-color: transparent;
                color: var(--tp-common-black);
                border-color: var(--tp-common-black);
            }
        }
    }
}
.tp-product-quantity{
	width: 100px;
	position: relative;
    border-radius: 20px;
}
.tp-cart-plus,
.tp-cart-minus{
    display: inline-block;
    text-align: center;
    font-size: 16px;
	color: var(--tp-common-black);
    transition: .3s;
	position: absolute;
	top: 50%;
	left: 16px;
	@include transform(translateY(-50%));

	& svg{
		@include transform(translateY(-2px));
	}
    &:hover{
        cursor: pointer;
        color: var(--tp-theme-1);
    }

	&.tp-cart-plus{
		left: auto;
		right: 16px;

		&::after{
			left: 0;
			right: auto;
		}
	}
}
.tp-cart-input[type="text"]{
    height:34px;
    text-align: center;
    font-size: 14px;
    border: 1px solid #f5f5f5;
	background-color: var(--tp-common-white);
    font-family: var(--tp-ff-marcellus);
	padding: 0 30px;
    border-radius: 20px;
	@include rtl{
		text-align: center;
	}
    &:focus{
        outline: none;
    }
}

.cartmini{
    $self : &;
    &__area{
        position: fixed;
        right: 0;
        top: 0;
        width: 320px;
        height: 100%;
        @include transform(translateX(calc(100% + 80px)));
        background: var(--tp-common-white)  none repeat scroll 0 0;
        z-index: 99999;
        scrollbar-width: none;
        transition: .3s;
        &::-webkit-scrollbar {
            display: none;
        }
        &.cartmini-opened{
            @include transform(translateX(0));
        }
    }
    &__wrapper{
        position: relative;
        min-height: 100%;
        padding-left: 25px;
        padding-right: 25px;
    }
    &__top{
        &-title{
            padding: 20px 0;
            border-bottom: 1px solid #E0E2E3;
            & h4{
                font-size: 18px;
                text-transform: capitalize;
                font-weight: 600;
                margin-bottom: 0;
            }
        }
    }
	&__close{
		position: absolute;
		top: 17px;
		right: 0;
		&-btn{
			background: transparent;
			color: var(--tp-common-black);
			font-size: 22px;
			&:hover{
				@include transform(rotate(90deg));
			}
		}
	}
    &__shipping{
        padding: 15px 0;
        border-bottom: 1px solid #E0E2E3;
        & .progress{
            height: 10px;
            border-radius: 0;
            &-bar{
                background-color: var(--tp-theme-1);
            }
        }

        & p{
            margin-bottom: 5px;
            font-size: 16px;
            & span{
                color: red;
                font-weight: 600;
            }
        }
    }
    &__widget{
        height: calc(100vh - 380px);
        overflow-y: scroll;
        overscroll-behavior-y: contain;
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none; /* for Chrome, Safari, and Opera */
        }
        &-item{
            position: relative;
            display: flex;
            padding: 20px 0;
            border-bottom: 1px solid rgba(129,129,129,.2);

            &:last-child{
                border-bottom: 0;
            }
        }
    }
    &__thumb{
        border: 1px solid #E0E2E3;
        margin-right: 15px;
        & img{
            width: 70px;
            height: auto;
        }
    }
    &__title{
        font-size: 15px;
        margin-bottom: 4px;
        font-weight: 500;
        & a{
            &:hover{
                color: var(--tp-theme-1);
            }
        }
    }
    &__content{
        padding-right: 15px;
        & .#{$theme-prifix}-product-quantity{
            width: 75px;
            padding: 0;

            .#{$theme-prifix}-cart-input[type="text"] {
                height: 30px;
                text-align: center;
                font-size: 13px;
                border: 1px solid #E0E2E3;
                background-color: var(--tp-common-white);
                padding: 0;
            }

            .#{$theme-prifix}-cart-plus, 
            .#{$theme-prifix}-cart-minus {
                width: 20px;
                height: 30px;
                line-height: 30px;
                display: inline-block;
                text-align: center;
                font-size: 13px;
                left: 3px;
                & svg{
                    @include transform(translateY(-1px));
                    width: 10px;
                }

                &::after{
                    display: none;
                }
            }

            & .#{$theme-prifix}-cart-plus{
                left: auto;
                right: 3px;
            }
        }

    }
    &__del{
        position: absolute;
        top: 15px;
        right: 0;
        width: 25px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        color: var(--tp-common-black);
        font-size: 14px;
        &:hover{
            color: var(--tp-theme-1);
        }
    }
    &__checkout{
        padding-top: 15px;
        padding-bottom: 85px;
        width: 100%;
        border-top: 2px solid #E0E2E3;
        &-title{
            & h4{
                font-size: 15px;
                display: inline-block;
                font-weight: 500;
                margin-bottom: 0;
                text-transform: capitalize;
            }
            & span{
                float: right;
                font-size: 15px;
                color: var(--tp-common-black);
                font-weight: 500;
            }
        }
    }
    &__price{
        font-size: 14px;
        font-weight: 500;
        color: var(--tp-common-black);
    }
    &__quantity{
        font-size: 12px;
        font-weight: 500;
    }
    &__empty{
        margin-top: 150px;

        & img{
            margin-bottom: 30px;
        }

        & p{
            font-size: 16px;
            color: var(--tp-common-black);
            margin-bottom: 15px;
        }
    }
}

.tp-cart-list{
    & .table{
        & tbody{
            & tr{
                border-bottom: 1px solid #d9d9d9;
            }
        }
    }
}
tbody, td, tfoot, th, thead, tr {
	border-style: none;
}
