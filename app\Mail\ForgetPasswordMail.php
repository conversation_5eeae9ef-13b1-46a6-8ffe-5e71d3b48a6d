<?php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ForgetPasswordMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $type;
 
    public $token;

    /**
     * Create a new message instance.
     */
    public function __construct($userCreated,$type,$resetToken)
    {
        $this->user = $userCreated;
        $this->type = $type;
     
        $this->token = $resetToken;
    }
 
    /**
     * Build the message.
     */
    public function build()
    {
        
        return $this->subject('Reset Password')
            ->view('emails.forgetPasswordMail')
            ->with([
                'userName' => $this->user->name,
                'userType' => $this->type,
                'email' => $this->user->email,
               
                'resetUrl' => route('password.reset', ['token' => $this->token]),
            ]);
    }
}
