<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Leads extends Model
{
    use HasFactory;

         // 🔽 Add this line to explicitly set the table name
    protected $table = 'leads';
     
    
    protected $fillable = [
        'id',
        'franchisee_id',
        'first_name',
        'last_name',
        'address',
        'phone',
        'email',
        'service',
        'status',
        'notes',
        'created_at',
        'updated_at'
    ];


    // Define the relationship with Franchisees
    public function franchisee()
    {
        return $this->belongsTo(Franchisees::class, 'franchisee_id');
    }

    
    


}