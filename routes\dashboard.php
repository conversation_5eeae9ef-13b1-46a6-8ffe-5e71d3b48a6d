<?php

use App\Http\Controllers\Dashboard\homeController;
use App\Http\Middleware\Authenticate;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Middleware\userRoleCheck;

Route::get('/privacy-policy', function () {
    return view('dashboard.privacyPolicy');
});

Route::get('/portal/login', [homeController::class, 'loginView'])->name('login');
Route::get('/portal/signup', [homeController::class, 'signupView'])->name('signup');
Route::get('/portal/forget-password', [homeController::class, 'forgetPasswordView'])->name('forget.password');
Route::get('/portal/password/reset/{token}', [homeController::class, 'showResetForm'])->name('password.reset');

Route::middleware([Authenticate::class])->group(function () {

  Route::get('/dashboard/home', [homeController::class, 'dashboardView'])->name('dashboard.home');
  Route::get('/dashboard/media', [homeController::class, 'mediaView']);
  Route::get('/dashboard/asset', [homeController::class, 'assetView']);
    Route::get('/dashboard/leaderboard', [homeController::class, 'leaderboardView']);

  //website setting page routes
  Route::get('/dashboard/website/setting', [homeController::class, 'webSettingView'])->name('website.setting');
});

Route::middleware([Authenticate::class, userRoleCheck::class . ':super_admin'])->group(function () {
  Route::get('/dashboard/profile', [homeController::class, 'profileView'])->name('business.profile');
  Route::get('/dashboard/business/user/update/{id}', [homeController::class, 'businessUserUpdateView'])->name('business.user.update');
});

Route::middleware([Authenticate::class,userRoleCheck::class . ':admin,super_admin'])->group(function () {
  //country routes
  Route::get('/dashboard/country', [homeController::class, 'countryView']);
  Route::get('/dashboard/country/update/{id}', [homeController::class, 'countryUpdateView'])->name('country.update');

  //state routes
  Route::get('/dashboard/states', [homeController::class, 'statesView']);
  Route::get('/dashboard/state/update/{id}', [homeController::class, 'stateUpdateView'])->name('state.update');

  // franchisee Routes
  Route::get('/dashboard/franchisee', [homeController::class, 'franchiseeView']);
  Route::get('/dashboard/franchisee/add', [homeController::class, 'addFranchiseeView']);
  Route::get('/dashboard/franchisee/update/{id}', [homeController::class, 'franchiseeUpdateView'])->name('franchisee.update');


  // franchisee Prospects
  Route::get('/dashboard/franchisee/prospects', [homeController::class, 'franchiseeProspectsView']);
  Route::get('/dashboard/franchisee/prospect/add', [homeController::class, 'addFranchiseeProspectView']);
  Route::get('/dashboard/franchisee/prospect/update/{id}', [homeController::class, 'franchiseeProspectUpdateView'])->name('franchiseeProspect.update');

});
Route::middleware([Authenticate::class,userRoleCheck::class . ':admin,super_admin,franchise'])->group(function () {

  // projects routes
  Route::get('/dashboard/projects', [homeController::class, 'projectsView']);
  Route::get('/dashboard/project/add', [homeController::class, 'addProjectView']);
  Route::get('/dashboard/projects/update/{id}', [homeController::class, 'projectUpdateView'])->name('project.update');

  // leads traker
  Route::get('/dashboard/leads', [homeController::class, 'leadsView']);
  Route::get('/dashboard/leads/add', [homeController::class, 'addleadsView']);
  Route::get('/dashboard/leads/update/{id}', [homeController::class, 'leadsUpdateView'])->name('leads.update');
  
});



