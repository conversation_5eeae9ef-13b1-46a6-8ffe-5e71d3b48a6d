<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Franchisees extends Model
{
    use HasFactory;

       // 🔽 Add this line to explicitly set the table name
    protected $table = 'franchisees';

  protected $fillable = [
        'id',
        'country_id',
         'state_id',
        'franchisee_name',
        'address',
        'phone_num',
        'email',
        'profile_img',
        'status',
        'created_at',
        'updated_at',
    ];
    public function country()
    {
        return $this->belongsTo(Countries::class, 'country_id');
    }
    public function state()
    {
        return $this->belongsTo(States::class, 'state_id');
    }
    public function projects()
    {
        return $this->hasMany(Projects::class, 'franchisee_id');
    }

    public function users()
    {
        return $this->hasMany(User::class, 'franchisee_id');
    }
}
