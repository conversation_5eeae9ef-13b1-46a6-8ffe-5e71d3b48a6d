<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Projects extends Model
{
    use HasFactory;

         // 🔽 Add this line to explicitly set the table name
    protected $table = 'projects';

    
    protected $fillable = [
        'id',
        'franchisee_id',
        'first_name',
        'last_name',
        'project_postcode',
        'project_value',
        'payment_type',
        'start_date',
        'end_date',
        'notes',
        'attachment',
        'project_status',
        'created_at',
        'updated_at'
    ];


    // Define the relationship with Franchisees
    public function franchisee()
    {
        return $this->belongsTo(Franchisees::class, 'franchisee_id');
    }

    
    


}