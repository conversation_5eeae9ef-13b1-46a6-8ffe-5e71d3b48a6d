<?php

use Carbon\Carbon;
use App\Mail\UserAccountMail;
use App\Mail\ForgetPasswordMail;
use App\Mail\SingleLeadMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

function media($file, $path)
{

    // $imageName = time() . '.' . $file->extension();
    // $imageName = time() . '_' . uniqid() . '.webp';

    // Get the original file extension
    $extension = $file->getClientOriginalExtension();

    // Define allowed image extensions
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];

    // If file is an image, rename with `.webp`, otherwise keep original extension
    if (in_array(strtolower($extension), $imageExtensions)) {
        $imageName = time() . '_' . uniqid() . '.webp';
    } else {
        $imageName = time() . '_' . uniqid() . '.' . $extension;
    }

    $file->move(public_path($path), $imageName);
    return $imageName;
}



function customDate($date, $format, $to_date = null)
{
    return Carbon::parse($date)->format($format);
}

function toCarbonDate($date, $boundary = 'start')
{
    try {
        $carbon = \Carbon\Carbon::createFromFormat('d M Y', trim($date));
        return $boundary === 'end' ? $carbon->endOfDay() : $carbon->startOfDay();
    } catch (\Exception $e) {
        return null;
    }
}

function sendAccountMail($userCreated, $type, $password, $resetToken)
{

    Mail::to($userCreated->email)->send(new UserAccountMail($userCreated, $type, $password, $resetToken));
}

function sendForgetPasswordMail($userCreated, $type, $resetToken)
{


    Mail::to($userCreated->email)->send(new ForgetPasswordMail($userCreated, $type, $resetToken));
}


function sendSingleLeadMail($name, $email, $subject, $message,$senderName,$senderEmail)
{
    try {
        Mail::to($email)->send((new SingleLeadMail($name, $subject, $message))->from($senderEmail ?? config('mail.from.address'),$senderName ?? config('mail.from.name')));
        return true;
    } catch (\Exception $e) {
        Log::error('SendSingleLeadMail error: ' . $e->getMessage());
        return false;
    }
}
