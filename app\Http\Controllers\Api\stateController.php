<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\States;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;


class stateController extends Controller
{




    public function storeState(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('states')->where(function ($query) use ($request) {
                    return $query->where('country_id', $request->country);
                }),
            ],
            'country' => 'required|exists:countries,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }
        // Create the state record
        $state = States::create([
            'name' => $request->name,
            'country_id' => $request->country,
        ]);

        return response()->json(['message' => 'state created successfully!', "status" => "success"], 200);
    }

    public function updatestate(Request $request, $id)
    {
        // Find the state by ID
        $state = States::findOrFail($id);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('states')->where(function ($query) use ($state) {
                    return $query->where('country_id', $state->country_id);
                })->ignore($id),
            ],
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }


        $state->name = $request->name;
        $state->save();

        return response()->json(['message' => 'state updated successfully!', "status" => "success"], 200);
    }

    public function deletestate($id)
    {
        // Find the ID
        $state = States::findOrFail($id);
        $state->delete();

        return response()->json(['message' => 'state deleted successfully!', "status" => "success"], 200);
    }
}
