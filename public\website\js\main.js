/***************************************************
==================== JS INDEX ======================
****************************************************
02. Mobile Menu Js

04. Menu Controls JS
05. Offcanvas Js

09. Body overlay Js
10. Sticky Header Js

13. Smooth Scroll Js

****************************************************/

(function ($) {
	"use strict";

	var windowOn = $(window);




	function back_to_top() {
		var btn = $('#back_to_top');
		var btn_wrapper = $('.back-to-top-wrapper');

		windowOn.scroll(function () {
			if (windowOn.scrollTop() > 300) {
				btn_wrapper.addClass('back-to-top-btn-show');
			} else {
				btn_wrapper.removeClass('back-to-top-btn-show');
			}
		});

		btn.on('click', function (e) {
			e.preventDefault();
			$('html, body').animate({ scrollTop: 0 }, '300');
		});
	}
	back_to_top();


	// 09. Body overlay Js
	$(".body-overlay").on("click", function () {
		$(".offcanvas__area").removeClass("opened");
		$(".tp-offcanvas-area").removeClass("opened");
		$(".body-overlay").removeClass("opened");
		$(".cartmini__area").removeClass("cartmini-opened");
	});


	// style 2
	$(".tp-offcanvas-open-btn").on("click", function () {
		$(".tp-offcanvas-2-area").addClass("opened");

		setTimeout(() => {
			$('.tp-text-hover-effect-word').addClass('animated-text');
		}, 900);
	});

	$(".tp-offcanvas-2-close-btn").on("click", function () {

		setTimeout(() => {
			$('.tp-text-hover-effect-word').removeClass('animated-text');
		}, 1200);

		$(".tp-offcanvas-2-area").removeClass("opened");
		$(".body-overlay").removeClass("opened");

	});

	// style 1
	$(".tp-offcanvas-open-btn").on("click", function () {
		$(".tp-offcanvas-area").addClass("opened");
		$(".body-overlay").addClass("opened");
	});


	$(".tp-offcanvas-close-btn").on("click", function () {
		$(".tp-offcanvas-area").removeClass("opened");
		$(".body-overlay").removeClass("opened");
	});

	if ($('.tp-main-menu-content').length && $('.tp-main-menu-mobile').length) {
		let navContent = document.querySelector(".tp-main-menu-content").outerHTML;
		let mobileNavContainer = document.querySelector(".tp-main-menu-mobile");
		mobileNavContainer.innerHTML = navContent;


		let arrow = $(".tp-main-menu-mobile .has-dropdown > a");

		arrow.each(function () {
			let self = $(this);
			let arrowBtn = document.createElement("BUTTON");
			arrowBtn.classList.add("dropdown-toggle-btn");
			arrowBtn.innerHTML = "<i class='fa-light fa-plus'></i>";

			self.append(function () {
				return arrowBtn;
			});

			self.find("button").on("click", function (e) {
				e.preventDefault();
				let self = $(this);
				self.toggleClass("dropdown-opened");
				self.parent().toggleClass("expanded");
				self.parent().parent().addClass("dropdown-opened").siblings().removeClass("dropdown-opened");
				self.parent().parent().children(".tp-submenu").slideToggle();
			});

		});
	}


	if ($('.tp-menu-fullwidth').length > 0) {

		var currentElement = $('.tp-menu-fullwidth');
		var previousDiv = currentElement.parent().parent();
		previousDiv.addClass('has-homemenu');

	}



	///////////////////////////////////////////////////
	// 07. Sticky Header Js
	// windowOn.on('scroll', function () {
	// 	var scroll = windowOn.scrollTop();
	// 	if (scroll < 20) {
	// 		$("#header-sticky").removeClass("header-sticky");
	// 	} else {
	// 		$("#header-sticky").addClass("header-sticky");
	// 	}
	// });




	// for header
	if ($("#tp-header-top__value-toogle").length > 0) {
		window.addEventListener('click', function (e) {

			if (document.getElementById('tp-header-top__value-toogle').contains(e.target)) {
				$(".tp-header-top__value-submenu").toggleClass("open");
			}
			else {
				$(".tp-header-top__value-submenu").removeClass("open");
			}
		});
	}



	///////////////////////////////////////////////////
	// 02. SubMenu Dropdown Toggle
	var header_icon =
		`<span class="header-icon">
		<svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path d="M6.04088 0L0.535156 4.125V11H4.26484V8.59381C4.26484 7.64165 5.05698 6.87506 6.04088 6.87506C7.02477 6.87506 7.81692 7.64165 7.81692 8.59381V11H11.5466V4.125L6.04088 0Z" fill="#FFB302"/></svg>                                
	</span>`;
	$(header_icon).insertBefore('.menu-icon nav ul .menu-icon-2');



	////////////////////////////////////////////////////
	// 13. Smooth Scroll Js
	function smoothSctoll() {
		$('.smooth a').on('click', function (event) {
			var target = $(this.getAttribute('href'));
			if (target.length) {
				event.preventDefault();
				$('html, body').stop().animate({
					scrollTop: target.offset().top - 120
				}, 1500);
			}
		});
	}
	smoothSctoll();



	// before-after
	if ($(".beforeAfter").length > 0) {
		$('.beforeAfter').beforeAfter({
			movable: true,
			clickMove: true,
			position: 50,
			separatorColor: '#fafafa',
			bulletColor: '#fafafa',
			onMoveStart: function (e) {

			},
			onMoving: function () {

			},
			onMoveEnd: function () {

			},
		});
	}

	if ($('#smooth-wrapper').length && $('#smooth-content').length) {
		gsap.registerPlugin(ScrollTrigger, ScrollSmoother, TweenMax, ScrollToPlugin);

		gsap.config({
			nullTargetWarn: false,
		});

		let smoother = ScrollSmoother.create({
			smooth: 2,
			effects: true,
			smoothTouch: 0.1,
			normalizeScroll: false,
			ignoreMobileResize: true,
		});

	}
})(jQuery);


$(document).ready(function () {
	var $slider = $('.gallery_slider');
	var $dotsContainer = $('.slider_dots');

	$slider.slick({
		dots: true,
		infinite: true,          // loop slides
		speed: 600,
		arrows: false,
		autoplay: true,          // auto play
		autoplaySpeed: 3000,     // 3 seconds per slide
		slidesToShow: 1,
		slidesToScroll: 1,
		appendDots: $dotsContainer,
		customPaging: function (slider, i) {
			var imgSrc = $(slider.$slides[i]).find('img').attr('src');
			return '<img src="' + imgSrc + '" class="slick-thumb"/>';
		}
	});

	// make dots scrollable horizontally
	$dotsContainer.addClass('dots-scrollable');

	// when slide changes, ensure active dot is in view
	$slider.on('afterChange', function (event, slick, currentSlide) {
		var $activeDot = $dotsContainer.find('li.slick-active');

		if ($activeDot.length) {
			var container = $dotsContainer.find('.slick-dots')[0];
			var active = $activeDot[0];

			// calculate scroll position
			var leftPos = active.offsetLeft - (container.clientWidth / 2) + (active.clientWidth / 2);

			// animate horizontal scroll only (no vertical jump)
			$(container).animate({ scrollLeft: leftPos }, 400);
		}
	});

	//////////////////////////

	$(".brand_slider").slick({
		speed: 5000,
		autoplay: true,
		autoplaySpeed: 0,
		cssEase: 'linear',
		slidesToShow: 6,
		slidesToScroll: 1,
		infinite: true,
		swipeToSlide: true,
		centerMode: true,
		focusOnSelect: false,
		dots: false,
		arrows: false,
		appendArrows: $('.brand_slider_arrow_box'),
		nextArrow: '<a class="brand_pre_arrow"><i class="fa fa-angle-right" aria-hidden="true"></i></a>',
		prevArrow: '<a class="brand_pre_arrow"><i class="fa fa-angle-left" aria-hidden="true"></i></a>',
		responsive: [
			{
				breakpoint: 1024,
				settings: {
					slidesToShow: 4,
					slidesToScroll: 1,
					autoplay: true,
					autoplaySpeed: 1000,
					dots: false,
					arrows: false,
				},
			},
			{
				breakpoint: 767,
				settings: {
					slidesToShow: 3,
					slidesToScroll: 1,
					autoplay: true,
					autoplaySpeed: 1000,
					dots: false,
					arrows: false,
				},
			},
			{
				breakpoint: 540,
				settings: {
					slidesToShow: 2,
					slidesToScroll: 1,
					autoplay: true,
					autoplaySpeed: 1000,
					dots: false,
					arrows: false,
				},
			},
			{
				breakpoint: 400,
				settings: {
					slidesToShow: 2,
					slidesToScroll: 1,
					autoplay: true,
					autoplaySpeed: 1000,
					dots: false,
					arrows: false,
				},
			},
		],
	});

	//////////////

	$(".cat_slider").slick({
		slidesToShow: 4,
		slidesToScroll: 1,
		autoplay: true,
		autoplaySpeed: 1000,
		dots: false,
		arrows: false,
		appendArrows: $('.cat_slider_arrow_box'),
		nextArrow: '<a class="brand_pre_arrow"><i class="fa fa-angle-right" aria-hidden="true"></i></a>',
		prevArrow: '<a class="brand_pre_arrow"><i class="fa fa-angle-left" aria-hidden="true"></i></a>',
		responsive: [
			{
				breakpoint: 1024,
				settings: {
					slidesToShow: 3,
					slidesToScroll: 1,
					autoplay: true,
					autoplaySpeed: 1000,
					dots: false,
					arrows: false,
				},
			},
			{
				breakpoint: 767,
				settings: {
					slidesToShow: 2,
					slidesToScroll: 1,
					autoplay: true,
					autoplaySpeed: 1000,
					dots: false,
					arrows: false,
				},
			},
			{
				breakpoint: 540,
				settings: {
					slidesToShow: 2,
					slidesToScroll: 1,
					autoplay: true,
					autoplaySpeed: 1000,
					dots: false,
					arrows: false,
				},
			},

		],
	});



	
        function initSliders() {
            // destroy if already initialized
            if ($('.slider-up').hasClass('slick-initialized')) {
                $('.slider-up').slick('unslick');
            }
            if ($('.slider-down').hasClass('slick-initialized')) {
                $('.slider-down').slick('unslick');
            }

            if ($(window).width() > 768) {
                // Desktop: vertical continuous
                $('.slider-up').slick({
                    vertical: true,
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 0,
                    speed: 4000,
                    cssEase: 'linear',
                    arrows: false,
                    pauseOnHover: false,
                    infinite: true,
                    centerMode: true,
                    centerPadding: '80px'
                });

                $('.slider-down').slick({
                    vertical: true,
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 0,
                    speed: 4000,
                    cssEase: 'linear',
                    arrows: false,
                    pauseOnHover: false,
                    infinite: true,
                    centerMode: true,
                    centerPadding: '80px'
                });

            } else {
                // Mobile: horizontal continuous
                $('.slider-up, .slider-down').slick({
                    slidesToShow: 3, // you can adjust how many slides visible on mobile
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 0, // continuous
                    speed: 4000,
                    cssEase: 'linear',
                    arrows: false,
                    pauseOnHover: false,
                    infinite: true
                });
            }
        }

        // init on load
        initSliders();

        // reinit on resize
        $(window).on('resize', function() {
            initSliders();
        });
});
