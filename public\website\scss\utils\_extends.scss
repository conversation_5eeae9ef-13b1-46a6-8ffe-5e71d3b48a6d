@use './mixins' as *;

%include-bg{
	@include background();
}

%transition{
	@include transition();
}

// svg

%svg-1{
	@include transform(translateY(-1px));
}

%svg-2{
	@include transform(translateY(-2px));
}

%svg-3{
	@include transform(translateY(-3px));
}

%svg-4{
	@include transform(translateY(-4px));
}


// font


%ff-dmsans{
	font-family: var(--tp-ff-dmsans);
}

%ff-didone{
	font-family: var(--tp-ff-didone);
}

%ff-didonewidemedium{
	font-family: var(--tp-ff-didoneWideMedium);
}

%ff-didonecompackdark{
	font-family: var(--tp-ff-didoneCompackDark);
}

%ff-garamond{
	font-family: var(--tp-ff-garamond);
}

%ff-kufam{
	font-family: var(--tp-ff-kufam);
}

%ff-abril{
	font-family: var(--tp-ff-abril);
}


%ff-fontawesome{
	font-family: var(--tp-ff-fontawesome);
}


/* transform */
%translateY2{
	@include transform(translateY(-50%));
}
%translateX2{
	@include transform(translateX(-50%));
}
%translate2{
	@include transform(translate(-50%, -50%));
}


// bg thumbnails
%bg-thumb{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

// Before After
%tp-bfaf{
	position: absolute;
	content: '';
}
