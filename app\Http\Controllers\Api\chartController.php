<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Franchisees;
use App\Models\Projects;
use App\Models\States;
use App\Models\Countries;
use App\Models\Leads;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;


class chartController extends Controller
{


    public function dashboardGraphData()
    {
        $user = Auth::user();
        $data = [];


        // If user is an admin
        if ($user->role->role_key == 'admin' || $user->role->role_key == 'super_admin') {

            $franchiseeProjectStats = Franchisees::withCount('projects')
                ->get()
                ->map(function ($franchisee) {
                    return [
                        'franchisee_name' => $franchisee->franchisee_name,
                        'project_count' => $franchisee->projects_count
                    ];
                });

            // ✅ Country-wise Franchisee Count
            $countryWiseFranchisees = Franchisees::with('country')
                ->get()
                ->groupBy('country.name')
                ->map(function ($group, $country) {
                    return [
                        'country' => $country,
                        'franchisee_count' => $group->count()
                    ];
                })->values();

            // ✅ State-wise Franchisee Count
            $stateWiseFranchisees = Franchisees::with('state')
                ->get()
                ->groupBy('state.name')
                ->map(function ($group, $state) {
                    return [
                        'state' => $state,
                        'franchisee_count' => $group->count()
                    ];
                })->values();

            // ✅ Country-wise Project Count
            $countryWiseProjects = Projects::with('franchisee.country')
                ->get()
                ->groupBy(fn($p) => optional($p->franchisee->country)->name)
                ->map(function ($group, $country) {
                    return [
                        'country' => $country,
                        'project_count' => $group->count()
                    ];
                })->values();

            // ✅ State-wise Project Count
            $stateWiseProjects = Projects::with('franchisee.state')
                ->get()
                ->groupBy(fn($p) => optional($p->franchisee->state)->name)
                ->map(function ($group, $state) {
                    return [
                        'state' => $state,
                        'project_count' => $group->count()
                    ];
                })->values();

            // ✅ Country-wise Revenue
            $countryWiseRevenue = Projects::with('franchisee.country')
                ->get()
                ->groupBy(fn($p) => optional($p->franchisee->country)->name)
                ->map(function ($group, $country) {
                    return [
                        'country' => $country,
                        'total_revenue' => $group->sum('project_value'),
                    ];
                })->values();

            // ✅ State-wise Revenue
            $stateWiseRevenue = Projects::with('franchisee.state')
                ->get()
                ->groupBy(fn($p) => optional($p->franchisee->state)->name)
                ->map(function ($group, $state) {
                    return [
                        'state' => $state,
                        'total_revenue' => $group->sum('project_value'),
                    ];
                })->values();

            $data['adminStats'] = [
                // other existing data...
                'franchiseeProjectStats' => $franchiseeProjectStats,
                'countryWiseFranchisees' => $countryWiseFranchisees,
                'stateWiseFranchisees' => $stateWiseFranchisees,
                'countryWiseProjects' => $countryWiseProjects,
                'stateWiseProjects' => $stateWiseProjects,
                'countryWiseRevenue' => $countryWiseRevenue,
                'stateWiseRevenue' => $stateWiseRevenue,

            ];
        } else if ($user->role->role_key == 'franchise') {


            $franchiseeId = $user->franchisee_id; // assuming franchisee_id is in users table

            $projects = Projects::where('franchisee_id', $franchiseeId)->get();



            $completedProjects = $projects->where('project_status', 1);

            $allProjectChartData = $projects->map(function ($project) {
                return [
                    'name' => $project->first_name ?? 'Unnamed',
                    'value' => $project->project_value,
                    'status' => $project->project_status == 1 ? 'Completed' : 'New',
                ];
            });

            $completedProjectChartData = $completedProjects->map(function ($project) {
                return [
                    'name' => $project->first_name ?? 'Unnamed',
                    'value' => $project->project_value,
                ];
            });

            $data['franchiseStats'] = [

                'all_project_chart_data' => $allProjectChartData,
                'completed_project_chart_data' => $completedProjectChartData,
            ];
        }

        return response()->json($data);
    }



    // for admin 
    public function getFilteredProjects(Request $request)
    {
        if ($request->has('franchise')) {

            $query = Projects::where('franchisee_id', $request->franchise);

            if ($request->filled('date_range')) {
                $dates = explode(' - ', $request->date_range); // Correct delimiter!

                if (count($dates) === 2) {
                    $startDate = toCarbonDate($dates[0], 'start');
                    $endDate   = toCarbonDate($dates[1], 'end');

                    if ($startDate && $endDate) {
                        $query->whereBetween('start_date', [$startDate, $endDate]);
                    }
                }
            }

            $projects = $query->get();

            $NumProjects = $projects->count();
            $totalRevenue = $projects->sum('project_value');

            $completedProjects = $projects->where('project_status', 1);
            $completedProjectCount = $completedProjects->count();
            $completedTotalRevenue = $completedProjects->sum('project_value');

            // All Projects Revenue Data
            $allProjectChartData = $projects->map(function ($project) {
                return [
                    'name' => $project->first_name,
                    'value' => $project->project_value,
                    'status' => $project->project_status == 1 ? 'Completed' : 'New',
                ];
            });

            // Completed Projects Revenue Data
            $completedProjectChartData = $completedProjects->map(function ($project) {
                return [
                    'name' => $project->first_name,
                    'value' => $project->project_value,
                ];
            });
            $fran = Franchisees::with(['country', 'state'])->where('id', $request->franchise)->first();
            return response()->json([
                'num_projects' => $NumProjects,
                'franchisee_country' => $fran?->country?->name ?? 'N/A',
                'franchisee_state' => $fran?->state?->name ?? 'N/A',

                'completed_projects' => $completedProjectCount,
                'total_revenue' => $totalRevenue,
                'completed_total_revenue' => $completedTotalRevenue,
                'all_project_chart_data' => $allProjectChartData,
                'completed_project_chart_data' => $completedProjectChartData,
            ]);
        }

        return response()->json(['error' => 'Invalid selection'], 400);
    }

    public function getProjectsRevenue(Request $request)
    {
        $query = Projects::query(); // Start as query builder, not collection

        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);

            if (count($dates) === 2) {
                $startDate = toCarbonDate($dates[0], 'start');
                $endDate   = toCarbonDate($dates[1], 'end');

                if ($startDate && $endDate) {
                    $query->whereBetween('start_date', [$startDate, $endDate]);
                }
            }
        }

        $projects = $query->get();

        $NumProjects = $projects->count();
        $totalRevenue = $projects->sum('project_value');

        $completedProjects = $projects->where('project_status', 1);
        $completedProjectCount = $completedProjects->count();
        $completedTotalRevenue = $completedProjects->sum('project_value');

        // All Projects Revenue Data
        $allProjectChartData = $projects->map(function ($project) {
            return [
                'name' => $project->first_name,
                'value' => $project->project_value,
                'status' => $project->project_status == 1 ? 'Completed' : 'New',
            ];
        });

        // Completed Projects Revenue Data
        $completedProjectChartData = $completedProjects->map(function ($project) {
            return [
                'name' => $project->first_name,
                'value' => $project->project_value,
            ];
        });

        return response()->json([
            'num_projects' => $NumProjects,
            'completed_projects' => $completedProjectCount,
            'total_revenue' => $totalRevenue,
            'completed_total_revenue' => $completedTotalRevenue,
            'all_project_chart_data' => $allProjectChartData,
            'completed_project_chart_data' => $completedProjectChartData,
        ]);
    }




    // for franchisee 
    public function getFilteredRevenue(Request $request)
    {

        $franchiseeId = Auth::user()->franchisee_id;
        $query = Projects::where('franchisee_id', $franchiseeId);

        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range); // Correct delimiter!

            if (count($dates) === 2) {
                $startDate = toCarbonDate($dates[0], 'start');
                $endDate   = toCarbonDate($dates[1], 'end');

                if ($startDate && $endDate) {
                    $query->whereBetween('start_date', [$startDate, $endDate]);
                }
            }
        }

        $projects = $query->get();

        $NumProjects = $projects->count();
        $totalRevenue = $projects->sum('project_value');

        $completedProjects = $projects->where('project_status', 1);
        $completedProjectCount = $completedProjects->count();
        $completedTotalRevenue = $completedProjects->sum('project_value');

        // All Projects Revenue Data
        $allProjectChartData = $projects->map(function ($project) {
            return [
                'name' => $project->first_name,
                'value' => $project->project_value,
                'status' => $project->project_status == 1 ? 'Completed' : 'New',
            ];
        });

        // Completed Projects Revenue Data
        $completedProjectChartData = $completedProjects->map(function ($project) {
            return [
                'name' => $project->first_name,
                'value' => $project->project_value,
            ];
        });

        return response()->json([
            'num_projects' => $NumProjects,
            'completed_projects' => $completedProjectCount,
            'total_revenue' => $totalRevenue,
            'completed_total_revenue' => $completedTotalRevenue,
            'all_project_chart_data' => $allProjectChartData,
            'completed_project_chart_data' => $completedProjectChartData,
        ]);


        return response()->json(['error' => 'Invalid selection'], 400);
    }
}
