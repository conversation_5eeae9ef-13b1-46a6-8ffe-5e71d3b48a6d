<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Roles;
use App\Models\Businesses;
use App\Models\Countries;
use App\Models\States;
use App\Models\Franchisees;
use App\Models\Projects;
use App\Models\Leads;
use App\Models\Franchiseeprospects;

class homeController extends Controller
{

    public function loginView()
    {
        if (auth::check()) {
            return redirect()->route('dashboard.home'); // Redirect to the dashboard if logged in
        }
        return view('dashboard.auth.login');
    }
    public function signupView()
    {
        if (auth::check()) {
            return redirect()->route('dashboard.home'); // Redirect to the dashboard if logged in
        }
        return view('dashboard.auth.signup');
    }
    public function forgetPasswordView()
    {
        if (auth::check()) {
            return redirect()->route('dashboard.home'); // Redirect to the dashboard if logged in
        }
        return view('dashboard.auth.forgetPassword');
    }


    public function showResetForm($token)
    {

        $user = User::where('password_reset_token', $token)->first();

        if (!$user) {
            abort(403, 'Invalid or expired token.');
        }
        return view('dashboard.auth.resetPassword', compact('token'));
    }

    public function assetView()
    {
        return view('dashboard.dashboardAssets');
    }
    public function mediaView()
    {
        $user = Auth::user();
        $business = Businesses::first();
        // $images = json_decode($business->media, true);

        // Retrieve and decode `user_media`
        $userMedia = json_decode($business->user_media, true) ?? [];

        // Get the images for the logged-in user
        $images = $userMedia[$user->id] ?? [];

        return view('dashboard.media', compact('images'));
    }



    public function dashboardView(Request $request)
    {

        $user = Auth::user();

        if ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin') {

            $franchisees = Franchisees::get();
            $numFranchisees = count($franchisees);

            $projects = Projects::get();
            $numProjects = count($projects);
            $completedProjects = Projects::where('project_status', 1)->get();
            $completedNumProjects = count($completedProjects);
            $totalRevenue = Projects::sum('project_value'); // Total revenue from all projects
            $completedTotalRevenue = Projects::where('project_status', 1)->sum('project_value');

            //  $leads = Leads::get();
              $leads = Franchiseeprospects::get();

            $totalLeads = $leads->count();
            $saleMadeLeads = $leads->where('status', 'sale made')->count();
            $contactedLeads = $leads->where('status', 'contacted, no answer')->count();
            $appointmentLeads = $leads->where('status', 'meeting booked')->count();
            $unsuccessfulLeads = $leads->where('status', 'unsuccessful lead')->count();
            $followupLeads = $leads->where('status', 'follow-up required')->count();
             

            $saleMadeRatio = $totalLeads > 0 ? round(($saleMadeLeads / $totalLeads) * 100, 2) : 0;
            $contactedRatio = $totalLeads > 0 ? round(($contactedLeads / $totalLeads) * 100, 2) : 0;
            $appointmentRatio = $totalLeads > 0 ? round(($appointmentLeads / $totalLeads) * 100, 2) : 0;
            $unsuccessfulRatio = $totalLeads > 0 ? round(($unsuccessfulLeads / $totalLeads) * 100, 2) : 0;
            $followupRatio = $totalLeads > 0 ? round(($followupLeads / $totalLeads) * 100, 2) : 0;
            $followupRatio = $totalLeads > 0 ? round(($followupLeads / $totalLeads) * 100, 2) : 0;



            return view('dashboard.index', compact('franchisees', 'numFranchisees', 'numProjects', 'totalRevenue', 'completedTotalRevenue', 'completedNumProjects','totalLeads','saleMadeRatio', 'contactedRatio', 'appointmentRatio', 'unsuccessfulRatio', 'followupRatio'));
        }
        if ($user->role->role_key === 'franchise') {

            $franchisees = Franchisees::with(['country', 'state'])->where('id', $user->franchisee_id)->first();

            $projects = Projects::where('franchisee_id', $franchisees->id)->get();
            $NumProjects = $projects->count();
            $totalRevenue = $projects->sum('project_value');

            $completedProjects = $projects->where('project_status', 1);
            $completedProjectCount = $completedProjects->count();
            $completedTotalRevenue = $completedProjects->sum('project_value');

            $leads = Leads::where('franchisee_id', $user->franchisee_id)->get();

            $totalLeads = $leads->count();
            $saleMadeLeads = $leads->where('status', 'sale made')->count();

            $saleMadeRatio = $totalLeads > 0 ? round(($saleMadeLeads / $totalLeads) * 100, 2) : 0;


            return view('dashboard.index', compact('NumProjects', 'totalRevenue', 'completedProjectCount', 'completedTotalRevenue', 'franchisees', 'saleMadeRatio'));
        }
        return view('dashboard.index');
    }



    public function leaderboardView()
    {
        $user = Auth::user();
        $data = [];

        $countries = Countries::get();

        // ✅ 1. Removed status filter for project count and sum (get all projects)
        $franchisees = Franchisees::with(['country', 'state'])
            ->withCount([
                'projects as completed_projects_count' // now counts ALL projects
            ])
            ->withSum([
                'projects as completed_projects_revenue'
            ], 'project_value')
            ->get();

        if ($user->role->role_key == 'admin' || $user->role->role_key == 'super_admin') {
            $grouped = $franchisees->groupBy(fn($f) => optional($f->country)->name ?? 'Unknown Country')
                ->map(function ($countryGroup) {
                    return $countryGroup->groupBy(fn($f) => optional($f->state)->name ?? 'Unknown State')
                        ->map(function ($stateGroup) {
                            $sorted = $stateGroup->sortByDesc('completed_projects_count')->values(); // ✅ 2. Rank by count
                            if ($sorted->isNotEmpty()) {
                                $sorted[0]->is_top_seller = true;
                            }
                            return $sorted;
                        });
                });

            $topCountryFranchisees = $franchisees
                ->sortByDesc('completed_projects_count') // ✅ Also apply same sort here
                ->groupBy(fn($item) => optional($item->country)->name ?? 'Unknown Country');

            $data = [
                'groupedFranchisees' => $grouped,
                'topCountryFranchisees' => $topCountryFranchisees,
                'countries' => $countries,
            ];
        } elseif ($user->role->role_key == 'franchise') {
            $franchisee = Franchisees::with(['country', 'state'])
                ->withCount([
                    'projects as completed_projects_count'
                ])
                ->withSum([
                    'projects as completed_projects_revenue'
                ], 'project_value')
                ->find($user->franchisee_id);

            // Load all franchisees once
            $allFranchisees = Franchisees::with(['country', 'state'])
                ->withCount([
                    'projects as completed_projects_count'
                ])
                ->withSum([
                    'projects as completed_projects_revenue'
                ], 'project_value')
                ->get();

            // ✅ State-wise rank
            $sameState = $allFranchisees->filter(fn($f) => $f->state_id === $franchisee->state_id);
            $stateSorted = $sameState->sortByDesc('completed_projects_count')->values();
            $stateRank = $stateSorted->search(fn($f) => $f->id == $franchisee->id) + 1;

            // ✅ Country-wise rank
            $sameCountry = $allFranchisees->filter(fn($f) => $f->country_id === $franchisee->country_id);
            $countrySorted = $sameCountry->sortByDesc('completed_projects_count')->values();
            $countryRank = $countrySorted->search(fn($f) => $f->id == $franchisee->id) + 1;

            $data = [
                'franchisee' => $franchisee,
                'rank' => $stateRank,
                'country_rank' => $countryRank,
                'country_name' => optional($franchisee->country)->name ?? 'Unknown Country',
                'state_name' => optional($franchisee->state)->name ?? 'Unknown State',
            ];
        }

        return view('dashboard.leaderBoard.index', $data);
    }




    // business profile view
    public function profileView()
    {
        $business_detail = Businesses::first();

        $adminRole = Roles::where('role_key', 'admin')->first();

        $user_detail = User::where('role_id', $adminRole->id)->first();

        $business_users = User::where('role_id', '!=', $adminRole->id)->get();

        $role_detail = Roles::whereNotIn('role_key', ['admin,super_admin'])->get();

        return view('dashboard.businessProfile.index', compact('business_detail', 'user_detail', 'role_detail', 'business_users'));
    }

    public function businessUserUpdateView($id)
    {
        $businessUser = User::findOrFail($id);

        return view('dashboard.businessProfile.user.update', compact('businessUser'));
    }

    public function webSettingView()
    {
        $webSettings = Businesses::first();
        $logos = json_decode($webSettings->business_logo);

        // Handle contact emails safely
        $contact_email = [];
        if (!empty($webSettings->contact_email)) {
            $decodedEmail = json_decode($webSettings->contact_email, true);
            if (is_array($decodedEmail)) {
                $contact_email = implode(',', array_map(fn($email) => $email['value'], $decodedEmail));
            }
        } else {
            $contact_email = ''; // Set an empty string to avoid htmlspecialchars() error
        }

        // Handle newsletter emails safely
        $newsletter_email = [];
        if (!empty($webSettings->newsletter_email)) {
            $decodedNewsletterEmail = json_decode($webSettings->newsletter_email, true);
            if (is_array($decodedNewsletterEmail)) {
                $newsletter_email = implode(',', array_map(fn($email) => $email['value'], $decodedNewsletterEmail));
            }
        } else {
            $newsletter_email = ''; // Set an empty string
        }

        return view('dashboard.webSettings.index', compact('logos', 'contact_email', 'newsletter_email'));
    }

    // country View routes
    public function countryView()
    {
        $countries = Countries::get();
        return view('dashboard.country.index', compact('countries'));
    }
    public function countryUpdateView($id)
    {
        $country = Countries::findOrFail($id);
        return view('dashboard.country.update', compact('country'));
    }

    // state View routes
    public function statesView()
    {
        $states = States::get();
        $countries = Countries::get();
        return view('dashboard.state.index', compact('states', 'countries'));
    }
    public function stateUpdateView($id)
    {
        $state = States::findOrFail($id);
        $countries = Countries::get();
        return view('dashboard.state.update', compact('state', 'countries'));
    }


    public function franchiseeView()
    {
        $franchisee = Franchisees::get();
        return view('dashboard.franchisee.index', compact('franchisee'));
    }
    public function addFranchiseeView()
    {
        $countries = Countries::get();
        return view('dashboard.franchisee.add', compact('countries'));
    }

    public function franchiseeUpdateView($id)
    {
        $franchise = Franchisees::findOrFail($id);
        $states = States::where('country_id', $franchise->country_id)->get();
        return view('dashboard.franchisee.update', compact('franchise', 'states'));
    }


    public function franchiseeProspectsView()
    {
        $franchiseeprospects = Franchiseeprospects::orderBy('created_at', 'desc')->get();
        return view('dashboard.franchiseeProspects.index', compact('franchiseeprospects'));
    }
    public function addFranchiseeProspectView()
    {
        return view('dashboard.franchiseeProspects.add');
    }

    public function franchiseeProspectUpdateView($id)
    {
        $franchise = Franchiseeprospects::findOrFail($id);
       
        return view('dashboard.franchiseeProspects.update', compact('franchise'));
    }


    // Association View routes
    public function projectsView()
    {
        $user = Auth::user();
        if ($user->role->role_key === 'franchise') {

            $projects = Projects::where('franchisee_id', $user->franchisee_id)->get();
            return view('dashboard.projects.index', compact('projects'));
        }

        $projects = Projects::get();
        return view('dashboard.projects.index', compact('projects'));
    }

    public function addProjectView()
    {
        return view('dashboard.projects.add');
    }
    public function projectUpdateView($id)
    {
        $project = Projects::findOrFail($id);
        return view('dashboard.projects.update', compact('project'));
    }

    // leads View routes
    public function leadsView()
    {

        $user = Auth::user();
        if ($user->role->role_key === 'franchise') {

            $leads = Leads::where('franchisee_id', $user->franchisee_id)->get();
            return view('dashboard.leads.index', compact('leads'));
        }

        $leads = Leads::get();
        return view('dashboard.leads.index', compact('leads'));
    }

    public function addleadsView()
    {
        return view('dashboard.leads.add');
    }
    public function leadsUpdateView($id)
    {
        $lead = Leads::findOrFail($id);
        return view('dashboard.leads.update', compact('lead'));
    }
     public function leadsEmailView($id)
    {
        $lead = Leads::findOrFail($id);
        return view('dashboard.leads.email', compact('lead'));
    }
}

