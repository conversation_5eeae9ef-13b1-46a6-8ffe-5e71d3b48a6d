@use '../utils' as *;
/*----------------------------------------*/
/*  2.9 Pagination
/*----------------------------------------*/

.#{$theme-prifix}-pagination{
	& ul{
		@include flexbox();
		
		& li{
			display: inline-block;
			&:not(:last-child){
				margin-right: 6px;
			}
			& a,
			& span{
				display: inline-block;
				width: 40px;
				height: 40px;
				line-height: 38px;
				text-align: center;
				border: 1px solid rgba($color: $black, $alpha: .1);
				font-size: 16px;
				font-weight: 500;
				color: var(--tp-text-body);
				&:hover,
				&.current{
					background: var(--tp-theme-primary);
					border-color: var(--tp-theme-primary);
					color: var(--tp-common-white);
				}

				& svg{
					@extend %svg-2;
				}
			}
		}
	}
}