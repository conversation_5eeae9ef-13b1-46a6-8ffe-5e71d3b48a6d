<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Roles;
use App\Models\States;
use App\Models\Franchisees;
use App\Models\Franchiseeprospects;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class franchiseeController extends Controller
{


    public function getStates($id)
    {
        $states = States::where('country_id', $id)->get();

        return response()->json([
            'status' => 200,
            'data' => $states,
        ]);
    }


    public function storeFranchisee(Request $request)
    {

        // Validate the request
        $validator = Validator::make($request->all(), [
            'franchisee_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('franchisees')->where(function ($query) use ($request) {
                    return $query->where('country_id', $request->country)
                        ->where('state_id', $request->state);
                }),
            ],
            'user_name' => 'required|string|max:255',
            'address' => 'required|string',
            'phone_num' => 'required|string|max:20',
            'email' => 'required|email',
            'password' => 'required|min:6',
            'country' => 'required|exists:countries,id',
            'state' => 'required|exists:states,id',
            'profile_img' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }


        $existingUser = User::where('email', $request->email)->first();
        $role = Roles::where('role_key', 'franchise')->first();

        if ($existingUser) {
            return response()->json(['error' => 'Email already taken Use another email.'], 422);
        }




        // Create the association record
        $franchise = Franchisees::create([
            'country_id' => $request->country,
            'state_id' => $request->state,
            'franchisee_name' => $request->franchisee_name,
            'address' => $request->address,
            'phone_num' => $request->phone_num,
            'email' => $request->email,
            'profile_img' => $request->profile_img,
            'status' => 1,
        ]);

        if ($franchise) {
            $resetToken = Str::random(60);
            // Create the user
            $userCreated = User::create([
                'name' => $request->user_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'franchisee_id' => $franchise->id,
                'role_id' => $role->id,
                'status' => 1,
                'password_reset_token' => $resetToken,
            ]);
        }
        if ($userCreated) {
            // Send Email to the New User
            sendAccountMail($userCreated, 'Franchisee admin', $request->password, $resetToken);
        }

        return response()->json(['message' => 'Franchisee created successfully!', "status" => "success"], 200);
    }

    public function updateFranchisee(Request $request, $id)
    {

        // Find the franchisee by ID
        $franchisee = Franchisees::findOrFail($id);
        // Validate the request
        $validator = Validator::make($request->all(), [
            'franchisee_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('franchisees')->where(function ($query) use ($request, $franchisee) {
                    return $query->where('country_id', $franchisee->country_id)
                        ->where('state_id', $request->state);
                })->ignore($franchisee->id), // <-- Important to ignore current ID when updating
            ],
            'address' => 'required|string',
            'phone_num' => 'required|string|max:20',
            'profile_img' => 'required',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Update operator data
        $franchisee->franchisee_name = $request->franchisee_name;
        $franchisee->address = $request->address;
        $franchisee->phone_num = $request->phone_num;
        $franchisee->profile_img = $request->profile_img;
        $franchisee->state_id = $request->state;
        $franchisee->save();

        return response()->json(['message' => 'Franchisee updated successfully!', "status" => "success"], 200);
    }

    public function deleteFranchisee($id)
    {

        $franchisee = Franchisees::findOrFail($id);
        $franchisee->delete();

        return response()->json(['message' => 'Franchisee deleted successfully!', "status" => "success"], 200);
    }

    public function franchiseeStatus(Request $request, $id)
    {

        $franchisee = Franchisees::findOrFail($id);
        $franchisee->status = $request->status;
        $franchisee->save();

        return response()->json(['message' => 'Franchisee status updated successfully!', "status" => "success"], 200);
    }

    //////////// Franchisee Prospect

    public function storeFranchiseeProspect(Request $request)
    {

        // Validate the request
        $validator = Validator::make($request->all(), [

            'f_name' => 'required|string|max:255',
            'l_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email',
            'postcode' => 'required|string|max:20',
            
            'licenced_builder' => 'required|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Create the association record
        $franchise = Franchiseeprospects::create([
            'first_name' => $request->f_name,
            'last_name' => $request->l_name,
            'phone' => $request->phone,
            'email' => $request->email,
            'postcode' => $request->postcode,
            'builders_licence_number' => $request->builders_licence_number,
           'licenced_builder' => $request->licenced_builder,
           'status' => 'follow-up required',
            'notes' => $request->notes,
        ]);


        return response()->json(['message' => 'Franchisee Prospect created successfully!', "status" => "success"], 200);
    }

    public function updateFranchiseeProspect(Request $request, $id)
    {

        // Find the franchisee by ID
        $franchisee = Franchiseeprospects::findOrFail($id);
        // Validate the request
        $validator = Validator::make($request->all(), [

            'f_name' => 'required|string|max:255',
            'l_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email',
            'postcode' => 'required|string|max:20',
           
            'licenced_builder' => 'required|string|max:20',
            'status' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }
        $franchisee->first_name = $request->f_name;
        $franchisee->last_name = $request->l_name;
        $franchisee->phone = $request->phone;
        $franchisee->email = $request->email;
        $franchisee->postcode = $request->postcode;
        $franchisee->builders_licence_number = $request->builders_licence_number;
        $franchisee->licenced_builder = $request->licenced_builder;
        $franchisee->notes = $request->notes;
         $franchisee->status = $request->status;
        $franchisee->save();

        return response()->json(['message' => 'Franchisee Prospects updated successfully!', "status" => "success"], 200);
    }

    public function deleteFranchiseeProspect($id)
    {

        $franchisee = Franchiseeprospects::findOrFail($id);
        $franchisee->delete();

        return response()->json(['message' => 'Franchisee Prospect deleted successfully!', "status" => "success"], 200);
    }


        public function sendProspectBulkMail(Request $request)
    {
        $request->validate([
            'lead_ids' => 'required|string',
            'email_subject' => 'required',
            'message' => 'required',
        ]);

        $leadIds = explode(',', $request->lead_ids);
        $leads = Franchiseeprospects::whereIn('id', $leadIds)->get();

        foreach ($leads as $lead) {
            try {
                $fullName = $lead->first_name . ' ' . $lead->last_name;
                sendSingleLeadMail($fullName, $lead->email, $request->email_subject, $request->message,$request->sender_name,$request->sender_email);
            } catch (\Exception $e) {
                Log::error("Bulk email failed for lead {$lead->id}: " . $e->getMessage());
            }
        }

        return response()->json([
            'message' => 'Bulk email sent successfully!',
            'status' => 'success'
        ], 200);
    }
}
