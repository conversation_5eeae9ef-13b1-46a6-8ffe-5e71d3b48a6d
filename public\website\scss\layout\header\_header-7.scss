@use '../../utils' as *;

/*----------------------------------------*/
/*  3.7 Header Style 7
/*----------------------------------------*/

.tp-header-7{
    &-area{
        position: fixed;
        width: 80px;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 99;
    }
    &-wrap{
        padding: 30px 25px;
        padding-bottom: 50px;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        background-color: var(--tp-common-black);
        box-shadow: 1px 1px 0px 0px rgba(255, 255, 255, 0.10);
    }
    &-menubar{
        line-height: 0;
        & button{
            line-height: 0;
            & span{
                height: 2px;
                width: 36px;
                display: inline-block;
                background-color: var(--tp-common-white);
                display: table;
                margin: 8px 0;
            }
            &:hover{
                & span{
                    animation: bar_anim 0.8s cubic-bezier(0.44, 1.1, 0.53, 0.99) 1 forwards;
                    &:nth-child(2) {
                        animation-delay: 0.1s;
                    }
                }
            }
        }
    }
    &-btn-box{
        transform: rotate(-90deg);
        line-height: 0;
        padding-left: 60px;
    }
}

.tp-header-7-lg{
    &-area{
        &.tp-header-7-area {
            position: relative;
            width: 100%;
        }
        & .tp-header-7-wrap {
            flex-direction: row;
            padding: 20px 25px;
        }
        & .tp-header-7-btn-box {
            transform: rotate(0deg);
            padding-left: 0;
        }
    }
}