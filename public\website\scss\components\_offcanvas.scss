@use '../utils' as *;

/*----------------------------------------*/
/*  2.10 Offcanvas
/*----------------------------------------*/
.body-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    visibility: hidden;
    opacity: 0;
    transition: 0.45s ease-in-out;
    background: rgba(24, 24, 24, 0.4);

    &.opened {
        opacity: 1;
        visibility: visible;
    }
}

// offcanvas 
.tp-offcanvas {
    &-logo{
        & img{
            width: 85px;
            height: 100%;
        }
    }
    &-area {
        position: fixed;
        top: 0;
        right: 0;
        width: 450px;
        height: 100%;
        z-index: 99;
        z-index: 99999;
        padding: 50px 50px;
        overflow-y: scroll;
        @extend %transition;
        overflow-x: hidden;
        scrollbar-width: none;
        background: #FFF;
        overscroll-behavior-y: contain;
        @include transform(translateX(calc(100% + 80px)));

        @media #{$xs} {
            width: 100%;
        }

        @media #{$sm} {
            width: 450px;
        }

        &.opened {
            @include transform(translateX(0));
        }

        & .tp-homemenu-wrapper {
            margin-top: 20px;

            & .gx-25 {
                --bs-gutter-x: 10px;
            }
        }

        & .homemenu-thumb-wrap {
            margin-bottom: 0;
            margin-bottom: 10px;
        }

        & .homemenu {
            margin-bottom: 15px;
        }

        & .homemenu-title {
            font-size: 13px;
        }

        & .tp-megamenu-list-box {
            padding: 0;
        }

        & .tp-megamenu-wrapper {
            padding: 20px 0px;
        }

        & .tp-megamenu-title {
            margin-bottom: 15px;
        }

        & .tp-megamenu-list-wrap ul {
            margin-left: 0;
        }

        & .tp-megamenu-list-wrap ul li a::before {
            top: 17px;
        }

        & .tp-main-menu-mobile ul li>a {
            font-size: 15px;
        }

        & .tp-megamenu-shop-style .tp-shop-banner-thumb {
            margin-bottom: 20px;
        }

        & .tp-megamenu-portfolio {
            padding-top: 20px;
        }

        & .tp-megamenu-portfolio-banner {
            display: none;
        }

        & .tp-megamenu-list-2 {
            padding-left: 0;
        }
    }
    &-top {
        margin-bottom: 90px;
    }
    &-close {
        &-btn {
            color: rgba($color: #000, $alpha: 1);

            &:hover {
                opacity: 1;
                @include transform(rotate(90deg));
            }
        }
    }
    &-content {
        margin-bottom: 45px;

        & p {
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            color: #414144;
        }
    }
    &-title {
        font-size: 40px;
        line-height: 1;
        letter-spacing: -0.8px;
        font-weight: 700;
        margin-bottom: 15px;
        color: var(--tp-common-black);

        &.sm {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
            text-transform: uppercase;
            color: var(--tp-common-black);
        }
    }
    &-gallery {
        margin-bottom: 65px;
    }
    &-contact {
        margin-bottom: 55px;

        & ul {
            & li {
                list-style: none;

                &:not(:last-child) {
                    margin-bottom: 2px;
                }
                & a {
                    display: inline-block;
                    color: #414144;
                    font-size: 18px;
                    position: relative;
                    &::after{
                        position: absolute;
                        bottom: 2px;
                        right: 0;
                        width: 0;
                        height: 1px;
                        content: '';
                        transition: .4s;
                        background-color: #1e1e1e;
                    }
                    &:hover{
                        color: var(--tp-common-black);
                        &::after{
                            width: 100%;
                            right: auto;
                            left: 0;
                        }
                    }
                }
            }
        }
    }
    &-social {
        & ul {
            @include flexbox();
            align-items: center;

            & li {
                list-style: none;

                &:not(:last-child) {
                    margin-right: 8px;
                }

                & a {
                    display: inline-block;
                    text-align: center;
                    width: 40px;
                    height: 40px;
                    line-height: 38px;
                    border-radius: 40px;
                    @extend %transition;
                    color: var(--tp-common-black);
                    border: 1px solid rgba(2, 11, 24, 0.10);

                    &:hover {
                        background-color: var(--tp-common-black);
                        border-color: var(--tp-common-black);
                        color: var(--tp-common-white);
                    }

                    & svg {
                        @include transform(translateY(-1px));
                    }
                }
            }
        }
    }
}

// offcanvas 2
.tp-offcanvas-2 {
    $self : &;
    &-area {
        position: relative;
        z-index: 99999;
        &.opened {
            & .animated-text {
                &>nav>ul>li a {

                    &::after {
                        visibility: visible;
                        opacity: 1;
                        bottom: 35px;
                    }

                    &::before {
                        width: 100%;
                    }

                    & .tp-text-hover-effect-word .single-char span {
                        @include transform(translateZ(.1px));
                    }
                }
            }

            #{$self} {

                &-left,
                &-right {
                    visibility: visible;
                    opacity: 1;
                }

                &-wrapper {
                    visibility: visible;
                    opacity: 1;
                }

                &-bg {

                    &.is-left,
                    &.is-right {
                        @include transform(scale(1, 1));
                        transition-delay: 0s;
                    }
                }

                &-menu {
                    transition-delay: 0s;
                }

                &-close {
                    visibility: visible;
                    opacity: 1;
                    @include transform(translateY(0));
                    transition-delay: .9s;
                }
            }

            & .tpoffcanvas__right-info,
            & .tpoffcanvas__social-link,
            & .tpoffcanvas__logo {
                visibility: visible;
                opacity: 1;
                @include transform(translateY(0));

            }

            & .tpoffcanvas__right-info {
                transition-delay: .9s;
            }

            .tpoffcanvas__social-link {
                transition-delay: .7s;
            }

            & .tpoffcanvas__logo {
                transition-delay: .3s;

                & img {
                    flex: 0 0 auto;
                }
            }
        }
        & .tp-main-menu-mobile {
            padding-top: 80px;
            padding-left: 90px;
            padding-right: 130px;
            @media #{$xl}{
                padding-left: 50px;
                padding-right: 50px;
            }
            @media #{$lg}{
                padding-left: 30px;
                padding-right: 30px;
            }
            @media #{$md,$xs}{
                padding: 30px;
            }
            & > nav {
                & > ul {
                    margin-bottom: 10px;
                    & > li {
                        & > a {
                            padding: 23px 0;
                            font-size: 62px;
                            font-weight: 700;
                            padding-left: 85px;
                            position: relative;
                            @media #{$lg}{
                                font-size: 50px;
                            }
                            @media #{$md}{
                                font-size: 40px;
                                padding-left: 0;
                            }
                            @media #{$xs}{
                                font-size: 30px;
                                padding-left: 0;
                            }
                            &::after {
                                left: 30px;
                                top: 35px;
                                font-size: 18px;
                                font-weight: 400;
                                position: absolute;
                                content: "0" counter(count);
                                counter-increment: count;
                                color: var(--tp-common-black);
                                transform: rotate(270deg) translateY(100%);
                                @media #{$md,$xs}{
                                    display: none;
                                }
                            }
                        }
                        & ul {
                            padding-top: 20px;
                            & li {
                                padding-left: 85px;
                                @media #{$md,$xs}{
                                    padding-left: 30px;
                                }
                                & a {
                                    width: 100%;
                                    font-size: 26px;
                                    font-weight: 500;
                                    border-bottom: 0;
                                    padding: 12px 0;
                                    text-transform: uppercase;
                                    transition: .4s;
                                    @media #{$md}{
                                        font-size: 20px;
                                    }
                                    @media #{$xs}{
                                        font-size: 18px;
                                    }
                                    &:hover{
                                        padding-left: 20px;
                                    }
                                }
                            }

                        }
                    }

                }
            }
            & nav ul li.has-dropdown > a .dropdown-toggle-btn {
                top: 0;
                right: 0;
                padding: 44px 20px;
                padding-left: 200px;
                @media #{$lg}{
                    padding: 28px 20px;
                    padding-left: 200px;
                }
                @media #{$md}{
                    padding: 33px 20px;
                    padding-left: 200px;
                }
                @media #{$xs}{
                    padding: 28px 20px;
                    padding-left: 200px;
                }
            }
            & nav > ul > li {
                border-bottom: 1px solid rgba(6, 7, 40, 0.1);
            }
        }
        & .tp-main-menu-mobile nav ul li:not(:last-child)  a {
            border-bottom: 0
        }
        & .tp-main-menu-mobile > nav > ul > li ul.submenu {
            border-top: 1px solid rgba(6, 7, 40, 0.1);
        }
        & .tp-main-menu-mobile nav ul li.has-dropdown > a .dropdown-toggle-btn {
            font-size: 22px;
        }
    }
    &-bg {
        &.left-box{
            position: fixed;
            top: 0;
            height: 100%;
            width: 60%;
            @include transition-mul((all 1s cubic-bezier(.77, 0, .175, 1)));
            z-index: 1111;
    
            @media #{$xs} {
                width: 100%;
            }
        }
        &.right-box{
            position: fixed;
            top: 0;
            height: 100%;
            width: 40%;
            @include transition-mul((all 1s cubic-bezier(.77, 0, .175, 1)));
            z-index: 1111;
    
            @media #{$xs} {
                width: 100%;
            }
        }
        &.is-left {
            left: 0;
            @include transform(scale(1, 0));
            transform-origin: top center;
            background: #F5F5F5;
            transition-delay: 1s;
        }
        &.is-right {
            right: 0;
            transform-origin: bottom center;
            @include transform(scale(1, 0));
            background-color: #FFFFFF;
            transition-delay: 1s;
        }
    }
    &-wrapper {
        & .tp-offcanvas-2-left {
            overflow-y: scroll;
        }

        & .left-box {
            position: fixed;
            top: 0;
            width: 60%;
            height: 100vh;
            overscroll-behavior-y: contain;
            scrollbar-width: none;
            z-index: 9999;
            padding-bottom: 50px;

            &::-webkit-scrollbar {
                display: none;
            }

            @media #{$xs} {
                width: 100%;
            }
        }
        & .right-box {
            position: fixed;
            top: 0;
            width: 40%;
            height: 100vh;
            overscroll-behavior-y: contain;
            scrollbar-width: none;
            z-index: 9999;
            padding-bottom: 50px;

            &::-webkit-scrollbar {
                display: none;
            }

            @media #{$xs} {
                width: 100%;
            }
        }
    }
    &-left {
        visibility: hidden;
        opacity: 0;
        left: 0;
        @extend %transition;
        transition-delay: 1.2s;
    }
    &-right {
        visibility: hidden;
        opacity: 0;
        right: 0;
        @extend %transition;
        transition-delay: 1.2s;

        &-inner {
            padding: 100px;
            @media #{$xl}{
                padding-left: 50px;
                padding-right: 50px;
            }
            @media #{$lg} {
                padding: 50px;
            }
            @media #{$md} {
                padding: 30px;
            }
        }
    }
    &-close {
        padding-top: 30px;
        padding-right: 90px;
        visibility: hidden;
        opacity: 0;
        @include transform(translateY(20px));
        transition-delay: .9s;
        @extend %transition;
        @media #{$md,$xs}{
            padding-right: 30px;
        }
        &-btn {
            color: var(--tp-common-black);
            text-transform: uppercase;
            font-size: 14px;
            font-weight: 500;
            @extend %ff-dmsans;
            & .text {
                width: 60px;
                height: 20px;
                overflow: hidden;
                color: var(--tp-common-black);
                @extend %transition;
                display: inline-block;
                transform: translateY(4px);

                & span {
                    display: inline-block;
                    @include transform(translateX(120%));
                    @extend %transition;
                }
            }

            &:hover {
                & .text {
                    & span {
                        transform: translateX(0%);
                    }
                }

                & span {
                    & svg {
                        @include transform(rotate(90deg));
                    }
                }
            }
        }
    }
    &-text{
        right: 0;
        bottom: 0;
        position: absolute;
        transform: rotate(-90deg) translateY(100%);
        & span{
            font-size: 320px;
            font-weight: 700;
            color: rgba($color: #212329, $alpha: 0.3);
            font-family: var(--tp-ff-shoulders);
        }
    }
}

.tpoffcanvas {
    &__logo {
        opacity: 0;
        padding-left: 90px;
        padding-top: 35px;
        visibility: hidden;
        @extend %transition;
        transition-delay: 0s;
        @include transform(translateY(30px));
        @media #{$md,$xs}{
            padding-left: 30px;
        }
        & img{
            width: 85px;
            height: 100%;
        }
    }
    &__right-wrap {
        height: 100%;
        padding: 100px;
        padding-right: 60px;
    }
    &__right-info {
        opacity: 0;
        text-align: right;
        visibility: hidden;
        @extend %transition;
        transition-delay: .6s;
        @include transform(translateY(60px));
    }
    &__tel a,
    &__mail a,
    &__text p {
        padding: 8px 0px;
        font-weight: 300;
        font-size: 22px;
        line-height: 20px;
        letter-spacing: 0.04em;
        color: var(--tp-common-black);
        display: inline-block;
        @media #{$md}{
            font-size: 18px;
        }
    }
    &__social-link {
        visibility: hidden;
        opacity: 0;
        @include transform(translateY(60px));
        transition-delay: .9s;
        @include transition-mul((all 1s ease-in-out));

        & ul {
            & li {
                list-style: none;

                &:not(:last-child) {
                    margin-bottom: 10px;
                }

                & a {
                    display: inline-block;
                    font-weight: 300;
                    font-size: 20px;
                    line-height: 1.3;
                    position: relative;
                    padding-left: 16px;
                    color: var(--tp-common-black);

                    @media #{$md} {
                        font-size: 22px;
                    }

                    &::before {
                        position: absolute;
                        content: '';
                        left: 16px;
                        bottom: 3px;
                        width: 0;
                        height: 1px;
                        background-color: var(--tp-common-black);
                        @extend %transition;
                    }

                    &:hover {
                        &::before {
                            width: calc(100% - 16px);

                        }
                    }
                }
            }
        }
    }
}

.tp-offcanvas-gallery-img{
    position: relative;
    &::after{
        position: absolute;
        top: 0;
        left: 0;
        content: '';
        height: 100%;
        width: 100%;
        opacity: 0;
        visibility: hidden;
        background-color: rgba(0, 0, 0, 0.3);
        transition: .4s;
    }
    & img{
        transition: .4s;
        width: 100%;
    }
    &:hover{
        & img{
            transform: scale(1.2);
        }
        &::after{
            opacity: 1;
            visibility: visible;
        }
    }
}