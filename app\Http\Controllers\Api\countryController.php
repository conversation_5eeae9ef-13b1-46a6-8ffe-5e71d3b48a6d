<?php

namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use App\Models\Countries;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;


class countryController extends Controller
{


  

    public function storeCountry(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:countries,name',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }
        // Create the country record
        $country = Countries::create([
            'name' => $request->name,
        ]);
        
        return response()->json(['message' => 'Country created successfully!', "status" => "success"], 200);
    }

    public function updateCountry(Request $request, $id)
    {
            // Find the country by ID
            $country = Countries::findOrFail($id);
       
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('countries', 'name')->ignore($id)
            ],
        ]);
      
    
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }
      $country->name = $request->name;
      $country->save();

        return response()->json(['message' => 'Country updated successfully!', "status" => "success"], 200);
    }

    public function deleteCountry($id)
    {
        // Find the ID
        $country = Countries::findOrFail($id);
        $country->delete();

        return response()->json(['message' => 'country deleted successfully!', "status" => "success"], 200);
    }
    
}
