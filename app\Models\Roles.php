<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Roles extends Model
{
    use HasFactory;

    
         // 🔽 Add this line to explicitly set the table name
    protected $table = 'roles';


    protected $fillable = [
        'id',
        'role',
        'role_key',
        'created_at',
        'updated_at',
    ];

     // Define the inverse relationship to the User model
     public function users()
     {
         return $this->hasMany(User::class);
     }


}