/************ TABLE OF CONTENTS ***************

	-----------------
    01. THEME CSS
	-----------------
		1.1 Theme Default

	-----------------
    02. COMPONENTS css
	-----------------
		2.1 Back to top
		2.2 Theme Settings
		2.3 Buttons

		2.6 Background 


		2.10 Offcanvas
	

	-----------------
    03. HEADER CSS
	-----------------

		3.3 Header Style 3
	

    ---------------------------------
	04. MENU CSS
	---------------------------------
		4.1 Main menu css

	---------------------------------
	05. BLO<PERSON> CSS
	---------------------------------
	    5.1 blog css start
		5.2 Postbox css
		5.3 Recent Post css
		5.4 Sidebar css






**********************************************/
/*----------------------------------------*/
/*  1.1 Theme Default
/*----------------------------------------*/


@font-face {
  font-family: 'Satoshi';
  src: url('/website/fonts/Satoshi.ttf') format('truetype'),
    url('/website/fonts/Satoshi.ttf') format('ttf');

}

:root {
  /**
  @font family declaration
  */
  --tp-ff-body: Satoshi;
  --tp-ff-heading: Satoshi;
  --tp-ff-p: Satoshi;
  --tp-ff-syne: Satoshi;
  --tp-ff-fontawesome: "Font Awesome 6 Pro";
  /**
  @color declaration
  */
  --tp-common-white: #F5F7F5;
  --tp-common-white-solid: #fff;
  --tp-common-black: #1E1E1E;
  --tp-common-black-2: #1E1E1E;
  --tp-common-black-3: #141414;
  --tp-common-dark: #121212;
  --tp-common-orange: #EB5939;
  --tp-grey-1: #262626;
  --tp-grey-2: #F4F4F4;
  --tp-grey-3: #5D5D63;
  --tp-grey-4: #F7F7F7;
  --tp-text-body: #5D5D63;
  --tp-theme-1: #336EF9;
  --tp-border-1: #EAEAEB;
}

.container,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  max-width: 1200px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: var(--tp-ff-body);
}

/*---------------------------------
	typography css start 
---------------------------------*/
body {
  font-family: var(--tp-ff-body);
  font-size: 14px;
  font-weight: normal;
  color: var(--tp-text-body);
  line-height: 26px;
  background: #EBF9FF;
}

.tp-product-tab-2 .nav-tabs .nav-link .tp-product-tab-tooltip,
.tp-tab-line,
.tp-offcanvas-area,
.tp-offcanvas-social ul li a,
.tp-offcanvas-2-left,
.tp-offcanvas-2-right,
.tp-offcanvas-2-close,
.tp-offcanvas-2-close-btn .text,
.tp-offcanvas-2-close-btn .text span,
.tpoffcanvas__logo,
.tpoffcanvas__right-info,
.tpoffcanvas__social-link ul li a::before,
.tp-swiper-dot .swiper-pagination-bullet,
.tp-swiper-dot-border .swiper-pagination-bullet,
.tp-swiper-scrollbar,
.back-to-top-wrapper,
a,
button,
p,
input,
select,
textarea,
li,
.transition-3 {
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -ms-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.tp-hotspot svg,
.tp-pagination ul li a svg,
.tp-pagination ul li span svg,
.tp-swiper-arrow button svg {
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  transform: translateY(-2px);
}

.tp-offcanvas-2-close-btn {
  font-family: var(--tp-ff-dmsans);
}

/* transform */
.tp-product-tab-2 .nav-tabs .nav-link:not(:first-child)::after {
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

header {
  position: absolute;
  width: 100%;
  top: 0px;
  left: 0px;
  z-index: 99;
}

a {
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--tp-ff-heading);
  color: var(--tp-common-black);
  margin-top: 0px;
  font-weight: 500;
  line-height: 1.2;
  -webkit-transition: color 0.3s ease-out;
  -moz-transition: color 0.3s ease-out;
  -ms-transition: color 0.3s ease-out;
  -o-transition: color 0.3s ease-out;
  transition: color 0.3s ease-out;
}

h1 {
  font-size: 40px;
}

h2 {
  font-size: 36px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 16px;
}

ul {
  margin: 0px;
  padding: 0px;
}

.table {
  --bs-table-bg: unset;
}

p {
  font-family: var(--tp-ff-p);
  color: var(--tp-text-body);
  margin-bottom: 15px;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
}

img {
  max-width: 100%;
}

a:not([href]):not([class]),
a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

input[type=text],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
input[type=url],
textarea {
  outline: none;
  background-color: #fff;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 14px;
  color: var(--tp-common-black);
  padding-left: 26px;
  padding-right: 26px;
  border: 1px solid #E0E2E3;
}

input[type=text]::-webkit-input-placeholder,
input[type=email]::-webkit-input-placeholder,
input[type=tel]::-webkit-input-placeholder,
input[type=number]::-webkit-input-placeholder,
input[type=password]::-webkit-input-placeholder,
input[type=url]::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #95999D;
}

input[type=text]::-moz-placeholder,
input[type=email]::-moz-placeholder,
input[type=tel]::-moz-placeholder,
input[type=number]::-moz-placeholder,
input[type=password]::-moz-placeholder,
input[type=url]::-moz-placeholder,
textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: #95999D;
}

input[type=text]:-moz-placeholder,
input[type=email]:-moz-placeholder,
input[type=tel]:-moz-placeholder,
input[type=number]:-moz-placeholder,
input[type=password]:-moz-placeholder,
input[type=url]:-moz-placeholder,
textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: #95999D;
}

input[type=text]:-ms-input-placeholder,
input[type=email]:-ms-input-placeholder,
input[type=tel]:-ms-input-placeholder,
input[type=number]:-ms-input-placeholder,
input[type=password]:-ms-input-placeholder,
input[type=url]:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #95999D;
}

input[type=text]::placeholder,
input[type=email]::placeholder,
input[type=tel]::placeholder,
input[type=number]::placeholder,
input[type=password]::placeholder,
input[type=url]::placeholder,
textarea::placeholder {
  /* MODERN BROWSER */
  color: #95999D;
}

[dir=rtl] input[type=text],
[dir=rtl] input[type=email],
[dir=rtl] input[type=tel],
[dir=rtl] input[type=number],
[dir=rtl] input[type=password],
[dir=rtl] input[type=url],
[dir=rtl] textarea {
  text-align: right;
}

input[type=text]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
input[type=url]:focus,
textarea:focus {
  border-color: var(--tp-common-black);
}

input[type=text]:focus::placeholder,
input[type=email]:focus::placeholder,
input[type=tel]:focus::placeholder,
input[type=number]:focus::placeholder,
input[type=password]:focus::placeholder,
input[type=url]:focus::placeholder,
textarea:focus::placeholder {
  opacity: 0;
}

textarea {
  line-height: 1.4;
  padding-top: 17px;
  padding-bottom: 17px;
}

textarea:focus {
  border-color: var(--tp-common-black);
}

textarea:focus::placeholder {
  opacity: 0;
}

input[type=color] {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  background: none;
  border: 0;
  cursor: pointer;
  height: 100%;
  width: 100%;
  padding: 0;
  border-radius: 50%;
}

*::-moz-selection {
  background: var(--tp-common-black);
  color: var(--tp-common-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--tp-common-black);
  color: var(--tp-common-white);
  text-shadow: none;
}

::selection {
  background: var(--tp-common-black);
  color: var(--tp-common-white);
  text-shadow: none;
}

*::-moz-placeholder {
  color: var(--tp-common-black);
  font-size: 14px;
  opacity: 1;
}

*::placeholder {
  color: var(--tp-common-black);
  font-size: 14px;
  opacity: 1;
}

.z-index {
  position: relative;
  z-index: 1;
}

.z-index-2 {
  position: relative;
  z-index: 2;
}

.z-index-3 {
  position: relative;
  z-index: 3;
}

.z-index-4 {
  position: relative;
  z-index: 4;
}

.z-index-5 {
  position: relative;
  z-index: 5;
}

.z-index-6 {
  position: relative;
  z-index: 6;
}

.z-index-7 {
  position: relative;
  z-index: 7;
}

.z-index-8 {
  position: relative;
  z-index: 8;
}

.z-index-9 {
  position: relative;
  z-index: 9;
}

.gx-5 {
  --bs-gutter-x: 5px;
}

.gx-10 {
  --bs-gutter-x: 10px;
}

.gx-15 {
  --bs-gutter-x: 15px;
}

.gx-20 {
  --bs-gutter-x: 20px;
}

.gx-25 {
  --bs-gutter-x: 25px;
}

.gx-30 {
  --bs-gutter-x: 30px;
}

.gx-35 {
  --bs-gutter-x: 35px;
}

.gx-40 {
  --bs-gutter-x: 40px;
}

.gx-45 {
  --bs-gutter-x: 45px;
}

.gx-50 {
  --bs-gutter-x: 50px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .gx-50 {
    --bs-gutter-x: 30px;
  }
}

.gx-60 {
  --bs-gutter-x: 60px;
}

.gx-75 {
  --bs-gutter-x: 75px;
}

.gx-80 {
  --bs-gutter-x: 80px;
}

.gx-140 {
  --bs-gutter-x: 140px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .gx-140 {
    --bs-gutter-x: 40px;
  }
}

.gx-90 {
  --bs-gutter-x: 90px;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .gx-90 {
    --bs-gutter-x: 50px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .gx-90 {
    --bs-gutter-x: 30px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
  .gx-90 {
    --bs-gutter-x: 50px;
  }
}

/*----------------------------------------
    Body Overlay 
-----------------------------------------*/
.body-overlay {
  background-color: var(--tp-common-black);
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  left: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -ms-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.body-overlay:hover {
  cursor: url("../img/icon/cross-out.png"), pointer;
}

.body-overlay.opened {
  opacity: 0.7;
  visibility: visible;
}

/* dropcap */
.tp-dropcap::first-letter {
  padding-left: 100px;
}

.class {
  stroke-dasharray: 189px, 191px;
  stroke-dashoffset: 0px;
}

/* gutter for x axis */
.tp-gx-20 {
  --bs-gutter-x: 20px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-gx-20 {
    --bs-gutter-x: 20px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-gx-20 {
    --bs-gutter-x: 20px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tp-gx-20 {
    --bs-gutter-x: 15px;
  }
}

@media (max-width: 767px) {
  .tp-gx-20 {
    --bs-gutter-x: 10px;
  }
}

.tp-gx-20 [class*=col-] {
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

/* gutter for x axis */
.tp-gx-30 {
  --bs-gutter-x: 30px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-gx-30 {
    --bs-gutter-x: 30px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-gx-30 {
    --bs-gutter-x: 30px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tp-gx-30 {
    --bs-gutter-x: 15px;
  }
}

@media (max-width: 767px) {
  .tp-gx-30 {
    --bs-gutter-x: 10px;
  }
}

.tp-gx-30 [class*=col-] {
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

/* gutter for x axis */
.tp-gx-40 {
  --bs-gutter-x: 40px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-gx-40 {
    --bs-gutter-x: 40px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-gx-40 {
    --bs-gutter-x: 30px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tp-gx-40 {
    --bs-gutter-x: 30px;
  }
}

@media (max-width: 767px) {
  .tp-gx-40 {
    --bs-gutter-x: 15px;
  }
}

.tp-gx-40 [class*=col-] {
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

@media (min-width: 1400px) {
  .container-large {
    max-width: 1325px;
  }
}

/*----------------------------------------*/
/*  2.1 Back to top
/*----------------------------------------*/
.back-to-top-wrapper {
  position: fixed;
  right: 50px;
  bottom: 0;
  height: 44px;
  width: 44px;
  cursor: pointer;
  display: block;
  border-radius: 50%;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
}

@media (max-width: 767px) {
  .back-to-top-wrapper {
    right: 20px;
    bottom: 20px;
  }
}

.back-to-top-wrapper.back-to-top-btn-show {
  visibility: visible;
  opacity: 1;
  bottom: 50px;
}

.back-to-top-btn {
  display: inline-block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background: var(--tp-common-white);
  box-shadow: 0px 8px 16px rgba(3, 4, 28, 0.3);
  color: var(--tp-common-black);
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -ms-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.back-to-top-btn svg {
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  transform: translateY(-2px);
}

.back-to-top-btn:hover {
  -webkit-transform: translateY(-4px);
  -moz-transform: translateY(-4px);
  -ms-transform: translateY(-4px);
  -o-transform: translateY(-4px);
  transform: translateY(-4px);
}

/*----------------------------------------*/
/*  2.2 Theme Settings
/*----------------------------------------*/
.tp-theme-settings-area {
  position: fixed;
  top: 50%;
  right: 0;
  width: 240px;
  background-color: #fff;
  border: 1px solid #EAEAEF;
  -webkit-transform: translateY(-50%) translateX(100%);
  -moz-transform: translateY(-50%) translateX(100%);
  -ms-transform: translateY(-50%) translateX(100%);
  -o-transform: translateY(-50%) translateX(100%);
  transform: translateY(-50%) translateX(100%);
  z-index: 991;
  direction: ltr;
  border-bottom-left-radius: 4px;
}

.tp-theme-settings-area.settings-opened {
  -webkit-transform: translateY(-50%) translateX(0%);
  -moz-transform: translateY(-50%) translateX(0%);
  -ms-transform: translateY(-50%) translateX(0%);
  -o-transform: translateY(-50%) translateX(0%);
  transform: translateY(-50%) translateX(0%);
}

.tp-theme-settings-area.settings-opened .tp-theme-settings-gear {
  opacity: 0;
}

.tp-theme-settings-area.settings-opened .tp-theme-settings-close {
  opacity: 1;
}

.tp-theme-settings-open {
  position: absolute;
  top: -1px;
  right: 100%;
}

.tp-theme-settings-open button {
  background-color: var(--tp-common-white);
  border: 1px solid #EAEAEF;
  border-left: 0;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 24px;
  color: var(--tp-common-black);
  position: relative;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.tp-theme-settings-gear {
  display: inline-block;
  -webkit-animation: spin 4s linear infinite;
  -moz-animation: spin 4s linear infinite;
  animation: spin 4s linear infinite;
}

@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-moz-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-ms-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.tp-theme-settings-close {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: 0;
}

.tp-theme-header-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 30px;
}

.tp-theme-wrapper {
  padding: 20px 30px 30px;
}

.tp-theme-toggle {
  text-align: center;
}

.tp-theme-toggle-main {
  display: inline-block;
  width: 160px;
  margin: auto;
  position: relative;
  z-index: 1;
  background-color: #f0f0f5;
  padding: 4px;
  border-radius: 20px;
}

.tp-theme-toggle-light,
.tp-theme-toggle-dark {
  display: inline-block;
  width: 48%;
  height: 26px;
  line-height: 26px;
}

.tp-theme-toggle input {
  display: none;
}

.tp-theme-toggle:hover {
  cursor: pointer;
}

.tp-theme-toggle label {
  color: var(--tp-common-black);
  font-size: 14px;
  font-weight: 500;
}

.tp-theme-toggle label:hover {
  cursor: pointer;
}

.tp-theme-toggle #tp-theme-toggler {
  display: none;
}

.tp-theme-toggle #tp-theme-toggler:checked+i {
  right: calc(50% - 4px);
}

.tp-theme-toggle-slide {
  position: absolute;
  top: 50%;
  right: 4px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 50%;
  height: 26px;
  color: var(--tp-common-black);
  background-color: var(--tp-common-white);
  border-radius: 30px;
  -webkit-transform: translate3d(0, 0);
  transform: translate3d(0, 0);
  -webkit-transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
  -o-transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
  transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
  z-index: -1;
}

.tp-theme-dir {
  text-align: center;
}

.tp-theme-dir-main {
  display: inline-block;
  width: 160px;
  margin: auto;
  position: relative;
  z-index: 1;
  background-color: #f0f0f5;
  padding: 4px;
  border-radius: 20px;
}

.tp-theme-dir-ltr,
.tp-theme-dir-rtl {
  display: inline-block;
  width: 48%;
  height: 25px;
  line-height: 27px;
}

.tp-theme-dir input {
  display: none;
}

.tp-theme-dir:hover {
  cursor: pointer;
}

.tp-theme-dir label {
  color: var(--tp-common-black);
  font-size: 14px;
  font-weight: 500;
}

.tp-theme-dir label:hover {
  cursor: pointer;
}

.tp-theme-dir #tp-dir-toggler {
  display: none;
}

.tp-theme-dir #tp-dir-toggler:checked+i {
  right: calc(50% - 4px);
}

.tp-theme-dir-slide {
  position: absolute;
  top: 50%;
  right: 4px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 50%;
  height: 26px;
  color: var(--tp-common-black);
  background-color: #fff;
  border-radius: 30px;
  -webkit-transform: translate3d(0, 0);
  transform: translate3d(0, 0);
  -webkit-transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
  -o-transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
  transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
  z-index: -1;
}

.tp-theme-color-item.active button::before {
  opacity: 1;
  visibility: visible;
}

.tp-theme-color-btn {
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  position: relative;
}

.tp-theme-color-btn::before {
  position: absolute;
  content: "\f00c";
  font-weight: 600;
  font-family: var(--tp-ff-fontawesome);
  color: var(--tp-common-white);
  font-size: 16px;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: 0;
  visibility: hidden;
}

.tp-theme-color-input {
  margin-top: 15px;
}

.tp-theme-color-input h6 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
}

.tp-theme-color-input label {
  display: inline-block;
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: var(--tp-theme-1);
}

.tp-theme-color-input label:hover {
  cursor: pointer;
}

.tp-theme-color-input input {
  display: none;
}

/*----------------------------------------*/
/*  2.3 Buttons
/*----------------------------------------*/
.tp-btn-black {
  height: 60px;
  line-height: 60px;
  border-radius: 40px;
  padding: 0 42px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
  transition-duration: 800ms;
}

.tp-btn-black::before {
  position: absolute;
  width: 200%;
  height: 200%;
  content: "";
  top: -200%;
  left: 50%;
  z-index: 1;
  border-radius: 50%;
  transition-duration: 800ms;
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  background: var(--tp-common-white);
  webkit-transition-duration: 800ms;
}

.tp-btn-black-wrap {
  position: relative;
  z-index: 1;
  float: left;
  overflow: hidden;
  display: inline-block;
}

.tp-btn-black-wrap .text-1 {
  position: relative;
  display: block;
  color: var(--tp-common-white);
  transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
}

.tp-btn-black-wrap .text-2 {
  position: absolute;
  top: 100%;
  display: block;
  color: var(--tp-common-black);
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

@media (max-width: 767px) {
  .tp-btn-black {
    height: 50px;
    line-height: 50px;
    padding: 0px 30px;
  }
}

.tp-btn-black:hover {
  color: var(--tp-common-white);
  background-color: transparent;
}

.tp-btn-black:hover::before {
  top: -40%;
}

.tp-btn-black:hover .tp-btn-black-wrap .text-1 {
  -webkit-transform: translateY(-150%);
  -ms-transform: translateY(-150%);
  transform: translateY(-150%);
}

.tp-btn-black:hover .tp-btn-black-wrap .text-2 {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.tp-btn-black-animated span {
  font-size: 26px;
  font-weight: 700;
  border-radius: 100px;
  display: inline-block;
  color: var(--tp-common-white);
  background-color: var(--tp-common-black);
  font-family: var(--tp-ff-shoulders);
  position: relative;
}

.tp-btn-black-animated span.btn-1 {
  padding: 0px 80px;
  height: 140px;
  line-height: 140px;
  transition: all 0.5s ease-in-out 0s;
}

.tp-btn-black-animated span.btn-2 {
  padding: 0px 36px;
  height: 140px;
  line-height: 140px;
  position: relative;
  left: -5px;
  transition: all 0.5s ease-in-out 0s;
}

.tp-btn-black-animated span.btn-3 {
  height: 60px;
  line-height: 60px;
  padding: 0px 43px;
  transform: rotate(-90deg);
  position: relative;
  left: -44px;
  transition: all 0.5s ease-in-out 0s;
}

.tp-btn-black-animated span.btn-expand {
  position: absolute;
  left: 0;
  height: 100%;
  width: 172px;
  border-radius: 100px;
  background-color: var(--tp-common-black);
  transition: all 0.5s ease-in-out 0s;
}

.tp-btn-black-animated:hover span.btn-expand {
  width: 295px;
}

.tp-btn-black-animated:hover span.btn-2 {
  left: -65px;
  padding: 0;
}

.tp-btn-black-animated:hover span.btn-3 {
  left: -53px;
  padding: 0;
  transform: rotate(0deg);
}

.tp-btn-black-2 {
  height: 60px;
  line-height: 55px;
  border-radius: 40px;
  padding: 0 18px 0 24px;
  font-weight: 500;
  font-size: 18px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
  border: 2px solid transparent;
}

.tp-btn-black-header {
  height: 50px;
  line-height: 55px;
  border-radius: 12px;
  padding: 0 18px 0 24px;
  font-weight: 500;
  font-size: 18px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: flex;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  background-color: #213E96;
  color: var(--tp-common-white);
  border: 2px solid transparent;
  align-items: center;
  margin-left: 15px;
}

.tp-btn-black-header:hover {
  background-color: transparent;
  color: var(--tp-common-black);
  border-color: #213E96;
}

.tp-btn-black-header:hover span .svg-icon {
  color: var(--tp-common-white);
}

.tp-btn-black-header:hover span .svg-bg {
  animation: rotate2 10s linear infinite;
}

.tp-btn-black-header span {
  margin-left: 12px;
}

.tp-btn-black-header span .svg-icon {
  position: absolute;
  top: 23px;
  left: 2px;
  right: 0;
  z-index: 2;
  margin: 0 auto;
  transition: 0.3s;
  color: var(--tp-common-black);
}

@media (max-width: 767px) {
  .tp-btn-black-2 {
    height: 50px;
    line-height: 43px;
    padding: 0px 30px;
  }

  .tp-btn-black-header span.p-relative svg.svg-bg {
    width: 20px;
    height: 20px;
  }

  .tp-btn-black-header span .svg-icon {
    left: 0px;
    width: 7px;
  }

  .tp-btn-black-header span {
    margin-left: 8px;
  }

  .tp-btn-black-header {
    height: 38px;
    line-height: 54px;
    padding: 0 10px 0 16px;
    font-size: 16px;
    margin-left: 15px;
  }
}



.tp-btn-black-2:hover {
  background-color: transparent;
  color: var(--tp-common-black);
  border-color: var(--tp-common-black);
}

.tp-btn-black-2:hover span .svg-icon {
  color: var(--tp-common-white);
}

.tp-btn-black-2:hover span .svg-bg {
  animation: rotate2 10s linear infinite;
}

.tp-btn-black-2 span {
  margin-left: 12px;
}

.tp-btn-black-2 span .svg-icon {
  position: absolute;
  top: 7px;
  left: -2px;
  right: 0;
  z-index: 2;
  margin: 0 auto;
  transition: 0.3s;
  color: var(--tp-common-black);
}

.tp-btn-black-square {
  height: 66px;
  line-height: 66px;
  padding: 0 35px;
  font-weight: 500;
  letter-spacing: -0.18px;
  font-size: 20px;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  border: 1.5px solid rgba(18, 18, 18, 0.2);
  color: var(--tp-common-black);
}

.tp-btn-black-square span {
  margin-left: 10px;
}

.tp-btn-black-square:hover {
  color: var(--tp-common-white);
  background-color: var(--tp-common-black);
  border-color: var(--tp-common-black);
}

.tp-btn-black-sm {
  height: 44px;
  line-height: 44px;
  border-radius: 4px;
  padding: 0 27px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  border: 2px solid transparent;
  color: var(--tp-common-white);
  background-color: var(--tp-common-black);
}

.tp-btn-black-sm:hover {
  color: var(--tp-common-black);
  border-color: var(--tp-common-black);
  background-color: transparent;
}

.tp-btn-black-md {
  height: 50px;
  line-height: 46px;
  padding: 0 27px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
  border: 2px solid transparent;
}

.tp-btn-black-md span {
  margin-left: 10px;
}

.tp-btn-black-md.white-bg {
  color: var(--tp-common-black-2);
}

.tp-btn-black-md.white-bg:hover {
  color: var(--tp-common-white);
  border-color: var(--tp-common-white);
  background-color: transparent;
}

.tp-btn-black-md:hover {
  color: var(--tp-common-black);
  border-color: var(--tp-common-black);
  background-color: transparent;
}

.tp-btn-white {
  height: 38px;
  line-height: 33px;
  border-radius: 100px;
  padding: 0 20px;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  background-color: var(--tp-common-white);
  color: var(--tp-common-black);
}

.tp-btn-white span {
  padding-left: 3px;
  color: var(--tp-common-black);
  transform: translateY(-1px);
  display: inline-block;
  transition: 0.3s;
}

.tp-btn-white:hover {
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
}

.tp-btn-white:hover span {
  color: var(--tp-common-white);
}

.tp-btn-white.background-black {
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
  border: 2px solid transparent;
  transition: 0.3s;
}

.tp-btn-white.background-black span {
  color: var(--tp-common-white);
}

.tp-btn-white.background-black:hover {
  background-color: transparent;
  border-color: var(--tp-common-black);
  color: var(--tp-common-black);
}

.tp-btn-white.background-black:hover span {
  color: var(--tp-common-black);
}

.tp-btn-white-lg {
  height: 50px;
  line-height: 50px;
  padding: 0 30px;
  font-size: 16px;
  z-index: 9;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  background-color: var(--tp-common-white);
  color: var(--tp-common-black);
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  transition: 0.4s;
}

.tp-btn-white-lg:hover {
  transform: scale(1.2);
  color: var(--tp-common-black);
}

.tp-btn-white-sm {
  z-index: 9;
  height: 30px;
  line-height: 30px;
  padding: 0 17px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 4px;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  white-space: nowrap;
  color: var(--tp-common-black);
  background-color: var(--tp-common-white);
}

.tp-btn-white-sm svg {
  margin-left: 5px;
  margin-top: -1px;
  width: 18px;
}

.tp-btn-white-sm:hover svg {
  -webkit-animation: iconMove ease-out 0.35s;
  animation: iconMove ease-out 0.35s;
}

.tp-btn-white-sm.border-style {
  height: 40px;
  line-height: 37px;
  font-size: 17px;
  background-color: transparent;
  color: var(--tp-common-white);
  border: 1px solid #fff;
  border-radius: 0;
}

.tp-btn-white-sm.border-style:hover {
  background-color: #fff;
  color: var(--tp-common-black);
}

.tp-btn-white-shape {
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  display: inline-block;
  padding: 32px 52px;
  text-align: center;
  transition: all 0.3s;
  border-radius: 100%;
  border: 2px solid transparent;
  color: var(--tp-common-black);
  background-color: var(--tp-common-white-solid);
}

.tp-btn-white-shape:hover {
  background-color: transparent;
  color: var(--tp-common-white);
  border-color: var(--tp-common-white-solid);
}

.tp-btn-zikzak {
  font-size: 18px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: -0.18px;
  color: var(--tp-common-black);
  z-index: 1;
}

.tp-btn-zikzak .zikzak-content {
  position: absolute;
  top: -17px;
  left: 43px;
  padding-left: 0;
}

.tp-btn-zikzak .zikzak-content svg {
  display: block;
  margin-top: 7px;
  transition: 0.7s;
}

.tp-btn-zikzak svg.anim {
  position: relative;
  z-index: -1;
}

.tp-btn-zikzak:hover {
  color: var(--tp-common-black);
}

.tp-btn-zikzak:hover svg.anim {
  animation: rotate2 10s linear infinite;
}

.tp-btn-zikzak:hover .zikzak-content svg {
  transform: translateX(60px);
}

.tp-btn-zikzak.zikzak-inner {
  color: var(--tp-common-white);
}

.tp-btn-zikzak.zikzak-inner .zikzak-content {
  left: 37px;
}

.tp-btn-zikzak.zikzak-inner:hover .zikzak-content svg {
  transform: translateX(40px);
}

.tp-btn-zikzak-sm {
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: -0.18px;
  color: var(--tp-common-black);
  text-align: left;
  transition: 0.3s ease-in-out;
}

.tp-btn-zikzak-sm .zikzak-content {
  position: absolute;
  top: -20px;
  left: 43px;
  z-index: 2;
}

.tp-btn-zikzak-sm .zikzak-content svg {
  display: block;
  margin-top: 7px;
  transition: 0.7s;
}

.tp-btn-zikzak-sm span .svg-bg {
  color: var(--tp-common-white-solid);
  transition: 0.3s ease-in-out;
}

.tp-btn-zikzak-sm:hover .zikzak-content {
  color: var(--tp-common-white-solid);
}

.tp-btn-zikzak-sm:hover .zikzak-content svg {
  transform: translateX(30px);
}

.tp-btn-zikzak-sm:hover span .svg-bg {
  color: var(--tp-common-black);
  animation: rotate2 10s linear infinite;
}

.tp-btn-project-sm {
  height: 32px;
  line-height: 32px;
  border-radius: 20px;
  padding: 0 22px;
  overflow: hidden;
  display: inline-block;
  position: relative;
  transition: all 0.3s;
  text-align: center;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.12);
}

.tp-btn-project-sm:hover {
  background-color: var(--tp-common-white);
  border-color: var(--tp-common-white);
  color: var(--tp-common-black);
}

.tp-btn-animation {
  border-top: 1px solid rgba(18, 18, 18, 0.1);
  border-bottom: 1px solid rgba(18, 18, 18, 0.1);
  height: 45px;
  line-height: 45px;
  padding: 0 17px;
  display: inline-block;
  white-space: nowrap;
  animation: scrollText 20s infinite linear;
}

.tp-btn-animation:hover {
  animation-play-state: paused;
}

.tp-btn-animation span {
  font-weight: 500;
  font-size: 16px;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: uppercase;
  position: relative;
  transition: all 0.3s;
  color: var(--tp-common-black);
}

.tp-btn-animation span::before {
  height: 5px;
  width: 5px;
  background-color: var(--tp-common-black);
  display: inline-block;
  content: "";
  margin-right: 15px;
  margin-left: 15px;
  border-radius: 50%;
  transform: translateY(-3px);
}

.tp-btn-circle {
  font-weight: 500;
  font-size: 18px;
  line-height: 1.22;
  letter-spacing: -0.01em;
  color: var(--tp-common-white);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: inline-block;
  width: 149px;
  height: 149px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tp-btn-circle.style-2 {
  border-color: rgba(25, 25, 26, 0.14);
  color: var(--tp-common-black);
}

.tp-btn-circle-icon {
  transform: translateY(10px);
  margin-left: 8px;
}

.tp-btn-circle .tp-btn-circle-dot {
  position: absolute;
  bottom: 0;
  left: 32px;
  width: 20px;
  height: 20px;
  -webkit-transition: all 0.6s ease-out;
  -moz-transition: all 0.6s ease-out;
  -ms-transition: all 0.6s ease-out;
  -o-transition: all 0.6s ease-out;
  transition: all 0.6s ease-out;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  background-color: var(--tp-common-white);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
}

.tp-btn-circle:hover {
  border: 1px solid transparent;
}

.tp-btn-circle:hover .tp-btn-circle-dot {
  width: 420px;
  height: 420px;
}

.tp-btn-circle:hover span {
  color: var(--tp-common-black);
}

.tp-btn-circle-2 {
  font-weight: 500;
  font-size: 18px;
  line-height: 1.22;
  letter-spacing: -0.01em;
  color: var(--tp-common-white);
  border: 1px solid rgba(255, 255, 255, 0.5);
  display: inline-block;
  width: 149px;
  height: 149px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.tp-btn-circle-2 span {
  text-align: center;
}

.tp-btn-circle-2 .tp-btn-circle-dot {
  position: absolute;
  bottom: 0;
  left: 32px;
  width: 20px;
  height: 20px;
  -webkit-transition: all 0.6s ease-out;
  -moz-transition: all 0.6s ease-out;
  -ms-transition: all 0.6s ease-out;
  -o-transition: all 0.6s ease-out;
  transition: all 0.6s ease-out;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  background-color: var(--tp-common-white);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: -1;
}

.tp-btn-circle-2:hover {
  border: 1px solid transparent;
}

.tp-btn-circle-2:hover .tp-btn-circle-dot {
  width: 420px;
  height: 420px;
}

.tp-btn-circle-2:hover span {
  color: var(--tp-common-black);
}

.tp-btn-orange {
  height: 50px;
  line-height: 50px;
  padding: 0 43px;
  font-size: 16px;
  font-size: 16px;
  font-weight: 600;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  color: var(--tp-common-white);
  background-color: var(--tp-common-orange);
  z-index: 9;
}

.tp-btn-orange:hover {
  color: var(--tp-common-white);
}

.tp-btn-border {
  height: 80px;
  line-height: 80px;
  border-radius: 40px;
  padding: 0 70px;
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  color: var(--tp-common-black);
  border: 1px solid var(--tp-border-1);
  transition-duration: 800ms;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-btn-border {
    height: 60px;
    line-height: 60px;
    border-radius: 40px;
    padding: 0 40px;
  }
}

.tp-btn-border::before {
  position: absolute;
  width: 200%;
  height: 200%;
  content: "";
  top: -200%;
  left: 50%;
  z-index: 1;
  border-radius: 50%;
  transition-duration: 800ms;
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  background: var(--tp-common-black);
  webkit-transition-duration: 800ms;
}

.tp-btn-border-wrap {
  position: relative;
  z-index: 1;
  float: left;
  overflow: hidden;
  display: inline-block;
}

.tp-btn-border-wrap .text-1 {
  position: relative;
  display: block;
  color: var(--tp-common-black);
  transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
}

.tp-btn-border-wrap .text-2 {
  position: absolute;
  top: 30%;
  display: block;
  color: var(--tp-common-white);
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}

@media (max-width: 767px) {
  .tp-btn-border {
    height: 60px;
    line-height: 60px;
    padding: 0px 50px;
  }
}

.tp-btn-border:hover {
  border-color: var(--tp-common-black);
}

.tp-btn-border:hover::before {
  top: -40%;
}

.tp-btn-border:hover .tp-btn-border-wrap .text-1 {
  -webkit-transform: translateY(-150%);
  -ms-transform: translateY(-150%);
  transform: translateY(-150%);
}

.tp-btn-border:hover .tp-btn-border-wrap .text-2 {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  opacity: 1;
  visibility: visible;
}

.tp-btn-border-lg {
  height: 60px;
  line-height: 58px;
  border-radius: 40px;
  padding: 0 30px;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.03em;
  z-index: 9;
  overflow: hidden;
  display: inline-block;
  text-transform: capitalize;
  position: relative;
  transition: all 0.3s;
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
  border: 2px solid transparent;
}

@media (max-width: 767px) {
  .tp-btn-border-lg {
    height: 50px;
    line-height: 50px;
    padding: 0px 30px;
  }
}

.tp-btn-border-lg:hover {
  color: var(--tp-common-black);
  border-color: var(--tp-common-black);
  background-color: transparent;
}

.tp-btn-border-lg span {
  margin-left: 12px;
}

.tp-btn-border-lg span .svg-icon {
  position: absolute;
  top: 7px;
  left: -3px;
  right: 0;
  z-index: 2;
  margin: 0 auto;
}

.tp-btn-border-sm {
  height: 44px;
  line-height: 42px;
  border-radius: 100px;
  padding: 0 30px;
  z-index: 9;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.16px;
  overflow: hidden;
  position: relative;
  display: inline-block;
  text-transform: uppercase;
  color: var(--tp-common-white);
  border: 1px solid var(--tp-common-white);
}

.tp-btn-border-sm:hover {
  color: var(--tp-common-black);
  background-color: var(--tp-common-white);
  border-color: var(--tp-common-white);
}

.tp-shop-btn {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  height: 50px;
  line-height: 50px;
  padding: 0px 45px;
  display: inline-block;
  text-transform: uppercase;
  color: var(--tp-common-white);
  font-family: var(--tp-ff-marcellus);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tp-shop-btn:hover {
  background-color: var(--tp-common-white);
  border-color: var(--tp-common-white);
  color: var(--tp-common-black);
}

.tp-shop-btn.border-style {
  border: 1px solid rgba(30, 30, 30, 0.2);
  color: var(--tp-common-black);
}

.tp-shop-btn.border-style:hover {
  background-color: var(--tp-common-black);
  border-color: var(--tp-common-black);
  color: var(--tp-common-white);
}

.tp-btn-shop-category {
  font-size: 16px;
  font-weight: 400;
  height: 46px;
  line-height: 46px;
  padding: 0px 30px;
  transition: 0.3s;
  display: inline-block;
  text-align: center;
  text-transform: uppercase;
  color: var(--tp-common-black);
  font-family: var(--tp-ff-marcellus);
  background-color: var(--tp-common-white-solid);
}

.tp-btn-shop-category:hover {
  background-color: var(--tp-common-black);
  color: var(--tp-common-white-solid);
}

.tp-btn-shop-category.black-bg {
  color: var(--tp-common-white);
}

.tp-filter-btn {
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  color: var(--tp-common-white);
  background-color: var(--tp-common-black);
  display: inline-block;
  padding: 13px 35px 9px;
  border: 1px solid var(--tp-common-black);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-filter-btn {
    padding: 13px 32px 9px;
  }
}

.tp-btn-subscribe {
  font-size: 16px;
  font-weight: 400;
  height: 54px;
  line-height: 54px;
  padding: 0px 27px;
  transition: 0.3s;
  display: inline-block;
  text-align: center;
  text-transform: uppercase;
  color: var(--tp-common-black);
  font-family: var(--tp-ff-marcellus);
  background-color: #fff;
}

.tp-btn-cart {
  font-size: 16px;
  font-weight: 500;
  height: 50px;
  line-height: 50px;
  padding: 0px 65px;
  transition: 0.3s;
  display: inline-block;
  text-align: center;
  text-transform: capitalize;
  color: var(--tp-common-white);
  font-family: var(--tp-ff-marcellus);
  background-color: var(--tp-common-black);
  flex: 0 0 auto;
  border: 2px solid transparent;
}

.tp-btn-cart:hover {
  background-color: transparent;
  border-color: var(--tp-common-black);
  color: var(--tp-common-black);
}

@media only screen and (min-width: 1600px) and (max-width: 1700px) {
  .tp-btn-cart {
    padding: 0px 35px;
  }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .tp-btn-cart {
    font-size: 14px;
    padding: 0px 15px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-btn-cart {
    padding: 0px 20px;
  }
}

.tp-btn-cart span {
  margin-right: 10px;
}

.tp-btn-cart.sm {
  height: 50px;
  line-height: 45px;
  padding: 0px 30px;
}

.tp-btn-submit {
  font-size: 17px;
  font-weight: 400;
  height: 50px;
  line-height: 48px;
  padding: 0px 35px;
  transition: 0.3s;
  display: inline-block;
  text-align: center;
  color: var(--tp-common-white);
  font-family: var(--tp-ff-marcellus);
  background-color: var(--tp-common-black);
  flex: 0 0 auto;
}

.tp-btn-submit span {
  margin-right: 10px;
}

.tp-btn-wishlist {
  font-weight: 400;
  height: 60px;
  width: 60px;
  line-height: 60px;
  font-size: 20px;
  transition: 0.3s;
  display: inline-block;
  text-align: center;
  text-transform: uppercase;
  color: var(--tp-common-black);
  border: 1px solid rgba(25, 25, 26, 0.12);
}

.tp-btn-wishlist:hover {
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
  border-color: var(--tp-common-black);
}

/*----------------------------------------*/
/*  2.6 Background
/*----------------------------------------*/
.white-bg {
  background-color: var(--tp-common-white);
}

.black-bg {
  background-color: var(--tp-common-black);
}

.black-bg-2 {
  background-color: var(--tp-common-black-2);
}

.black-bg-3 {
  background-color: var(--tp-common-black-3);
}

.dark-bg {
  background-color: var(--tp-common-dark);
}

.grey-bg {
  background-color: var(--tp-grey-1);
}

.grey-bg-2 {
  background-color: var(--tp-grey-4);
}

.grey-bg-3 {
  background-color: rgba(25, 25, 26, 0.1);
}

/*----------------------------------------*/
/*  2.10 Offcanvas
/*----------------------------------------*/
.body-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  visibility: hidden;
  opacity: 0;
  transition: 0.45s ease-in-out;
  background: rgba(24, 24, 24, 0.4);
}

.body-overlay.opened {
  opacity: 1;
  visibility: visible;
}

.tp-offcanvas-logo img {
  width: 85px;
  height: 100%;
}

.tp-offcanvas-area {
  position: fixed;
  top: 0;
  right: 0;
  width: 450px;
  height: 100%;
  z-index: 99;
  z-index: 99999;
  padding: 50px 50px;
  overflow-y: scroll;
  overflow-x: hidden;
  scrollbar-width: none;
  background: #FFF;
  overscroll-behavior-y: contain;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
}

@media (max-width: 767px) {
  .tp-offcanvas-area {
    width: 100%;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tp-offcanvas-area {
    width: 450px;
  }
}

.tp-offcanvas-area.opened {
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}

.tp-offcanvas-area .tp-homemenu-wrapper {
  margin-top: 20px;
}

.tp-offcanvas-area .tp-homemenu-wrapper .gx-25 {
  --bs-gutter-x: 10px;
}

.tp-offcanvas-area .homemenu-thumb-wrap {
  margin-bottom: 0;
  margin-bottom: 10px;
}

.tp-offcanvas-area .homemenu {
  margin-bottom: 15px;
}

.tp-offcanvas-area .homemenu-title {
  font-size: 13px;
}

.tp-offcanvas-area .tp-megamenu-list-box {
  padding: 0;
}

.tp-offcanvas-area .tp-megamenu-wrapper {
  padding: 20px 0px;
}

.tp-offcanvas-area .tp-megamenu-title {
  margin-bottom: 15px;
}

.tp-offcanvas-area .tp-megamenu-list-wrap ul {
  margin-left: 0;
}

.tp-offcanvas-area .tp-megamenu-list-wrap ul li a::before {
  top: 17px;
}

.tp-offcanvas-area .tp-main-menu-mobile ul li>a {
  font-size: 15px;
}

.tp-offcanvas-area .tp-megamenu-shop-style .tp-shop-banner-thumb {
  margin-bottom: 20px;
}

.tp-offcanvas-area .tp-megamenu-portfolio {
  padding-top: 20px;
}

.tp-offcanvas-area .tp-megamenu-portfolio-banner {
  display: none;
}

.tp-offcanvas-area .tp-megamenu-list-2 {
  padding-left: 0;
}

.tp-offcanvas-top {
  margin-bottom: 90px;
}

.tp-offcanvas-close-btn {
  color: black;
}

.tp-offcanvas-close-btn:hover {
  opacity: 1;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

.tp-offcanvas-content {
  margin-bottom: 45px;
}

.tp-offcanvas-content p {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #414144;
}

.tp-offcanvas-title {
  font-size: 40px;
  line-height: 1;
  letter-spacing: -0.8px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--tp-common-black);
}

.tp-offcanvas-title.sm {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 15px;
  text-transform: uppercase;
  color: var(--tp-common-black);
}

.tp-offcanvas-gallery {
  margin-bottom: 65px;
}

.tp-offcanvas-contact {
  margin-bottom: 55px;
}

.tp-offcanvas-contact ul li {
  list-style: none;
}

.tp-offcanvas-contact ul li:not(:last-child) {
  margin-bottom: 2px;
}

.tp-offcanvas-contact ul li a {
  display: inline-block;
  color: #414144;
  font-size: 18px;
  position: relative;
}

.tp-offcanvas-contact ul li a::after {
  position: absolute;
  bottom: 2px;
  right: 0;
  width: 0;
  height: 1px;
  content: "";
  transition: 0.4s;
  background-color: #1e1e1e;
}

.tp-offcanvas-contact ul li a:hover {
  color: var(--tp-common-black);
}

.tp-offcanvas-contact ul li a:hover::after {
  width: 100%;
  right: auto;
  left: 0;
}

.tp-offcanvas-social ul {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}

.tp-offcanvas-social ul li {
  list-style: none;
}

.tp-offcanvas-social ul li:not(:last-child) {
  margin-right: 8px;
}

.tp-offcanvas-social ul li a {
  display: inline-block;
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 38px;
  border-radius: 40px;
  color: var(--tp-common-black);
  border: 1px solid rgba(2, 11, 24, 0.1);
}

.tp-offcanvas-social ul li a:hover {
  background-color: var(--tp-common-black);
  border-color: var(--tp-common-black);
  color: var(--tp-common-white);
}

.tp-offcanvas-social ul li a svg {
  -webkit-transform: translateY(-1px);
  -moz-transform: translateY(-1px);
  -ms-transform: translateY(-1px);
  -o-transform: translateY(-1px);
  transform: translateY(-1px);
}

.tp-offcanvas-2-area {
  position: relative;
  z-index: 99999;
}

.tp-offcanvas-2-area.opened .animated-text>nav>ul>li a::after {
  visibility: visible;
  opacity: 1;
  bottom: 35px;
}

.tp-offcanvas-2-area.opened .animated-text>nav>ul>li a::before {
  width: 100%;
}

.tp-offcanvas-2-area.opened .animated-text>nav>ul>li a .tp-text-hover-effect-word .single-char span {
  -webkit-transform: translateZ(0.1px);
  -moz-transform: translateZ(0.1px);
  -ms-transform: translateZ(0.1px);
  -o-transform: translateZ(0.1px);
  transform: translateZ(0.1px);
}

.tp-offcanvas-2-area.opened .tp-offcanvas-2-left,
.tp-offcanvas-2-area.opened .tp-offcanvas-2-right {
  visibility: visible;
  opacity: 1;
}

.tp-offcanvas-2-area.opened .tp-offcanvas-2-wrapper {
  visibility: visible;
  opacity: 1;
}

.tp-offcanvas-2-area.opened .tp-offcanvas-2-bg.is-left,
.tp-offcanvas-2-area.opened .tp-offcanvas-2-bg.is-right {
  -webkit-transform: scale(1, 1);
  -moz-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  transform: scale(1, 1);
  transition-delay: 0s;
}

.tp-offcanvas-2-area.opened .tp-offcanvas-2-menu {
  transition-delay: 0s;
}

.tp-offcanvas-2-area.opened .tp-offcanvas-2-close {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  transition-delay: 0.9s;
}

.tp-offcanvas-2-area.opened .tpoffcanvas__right-info,
.tp-offcanvas-2-area.opened .tpoffcanvas__social-link,
.tp-offcanvas-2-area.opened .tpoffcanvas__logo {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.tp-offcanvas-2-area.opened .tpoffcanvas__right-info {
  transition-delay: 0.9s;
}

.tp-offcanvas-2-area.opened .tpoffcanvas__social-link {
  transition-delay: 0.7s;
}

.tp-offcanvas-2-area.opened .tpoffcanvas__logo {
  transition-delay: 0.3s;
}

.tp-offcanvas-2-area.opened .tpoffcanvas__logo img {
  flex: 0 0 auto;
}

.tp-offcanvas-2-area .tp-main-menu-mobile {
  padding-top: 80px;
  padding-left: 90px;
  padding-right: 130px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile {
    padding-left: 50px;
    padding-right: 50px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile {
    padding: 30px;
  }
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul {
  margin-bottom: 10px;
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li>a {
  padding: 23px 0;
  font-size: 62px;
  font-weight: 700;
  padding-left: 85px;
  position: relative;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li>a {
    font-size: 50px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li>a {
    font-size: 40px;
    padding-left: 0;
  }
}

@media (max-width: 767px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li>a {
    font-size: 30px;
    padding-left: 0;
  }
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li>a::after {
  left: 30px;
  top: 35px;
  font-size: 18px;
  font-weight: 400;
  position: absolute;
  content: "0" counter(count);
  counter-increment: count;
  color: var(--tp-common-black);
  transform: rotate(270deg) translateY(100%);
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li>a::after {
    display: none;
  }
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul {
  padding-top: 20px;
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul li {
  padding-left: 85px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul li {
    padding-left: 30px;
  }
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul li a {
  width: 100%;
  font-size: 26px;
  font-weight: 500;
  border-bottom: 0;
  padding: 12px 0;
  text-transform: uppercase;
  transition: 0.4s;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul li a {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul li a {
    font-size: 18px;
  }
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul li a:hover {
  padding-left: 20px;
}

.tp-offcanvas-2-area .tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn {
  top: 0;
  right: 0;
  padding: 44px 20px;
  padding-left: 200px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn {
    padding: 28px 20px;
    padding-left: 200px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn {
    padding: 33px 20px;
    padding-left: 200px;
  }
}

@media (max-width: 767px) {
  .tp-offcanvas-2-area .tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn {
    padding: 28px 20px;
    padding-left: 200px;
  }
}

.tp-offcanvas-2-area .tp-main-menu-mobile nav>ul>li {
  border-bottom: 1px solid rgba(6, 7, 40, 0.1);
}

.tp-offcanvas-2-area .tp-main-menu-mobile nav ul li:not(:last-child) a {
  border-bottom: 0;
}

.tp-offcanvas-2-area .tp-main-menu-mobile>nav>ul>li ul.submenu {
  border-top: 1px solid rgba(6, 7, 40, 0.1);
}

.tp-offcanvas-2-area .tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn {
  font-size: 22px;
}

.tp-offcanvas-2-bg.left-box {
  position: fixed;
  top: 0;
  height: 100%;
  width: 60%;
  -webkit-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  -moz-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  -ms-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  -o-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  z-index: 1111;
}

@media (max-width: 767px) {
  .tp-offcanvas-2-bg.left-box {
    width: 100%;
  }
}

.tp-offcanvas-2-bg.right-box {
  position: fixed;
  top: 0;
  height: 100%;
  width: 40%;
  -webkit-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  -moz-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  -ms-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  -o-transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  transition: all 1s cubic-bezier(0.77, 0, 0.175, 1);
  z-index: 1111;
}

@media (max-width: 767px) {
  .tp-offcanvas-2-bg.right-box {
    width: 100%;
  }
}

.tp-offcanvas-2-bg.is-left {
  left: 0;
  -webkit-transform: scale(1, 0);
  -moz-transform: scale(1, 0);
  -ms-transform: scale(1, 0);
  -o-transform: scale(1, 0);
  transform: scale(1, 0);
  transform-origin: top center;
  background: #F5F5F5;
  transition-delay: 1s;
}

.tp-offcanvas-2-bg.is-right {
  right: 0;
  transform-origin: bottom center;
  -webkit-transform: scale(1, 0);
  -moz-transform: scale(1, 0);
  -ms-transform: scale(1, 0);
  -o-transform: scale(1, 0);
  transform: scale(1, 0);
  background-color: #FFFFFF;
  transition-delay: 1s;
}

.tp-offcanvas-2-wrapper .tp-offcanvas-2-left {
  overflow-y: scroll;
}

.tp-offcanvas-2-wrapper .left-box {
  position: fixed;
  top: 0;
  width: 60%;
  height: 100vh;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
  z-index: 9999;
  padding-bottom: 50px;
}

.tp-offcanvas-2-wrapper .left-box::-webkit-scrollbar {
  display: none;
}

@media (max-width: 767px) {
  .tp-offcanvas-2-wrapper .left-box {
    width: 100%;
  }
}

.tp-offcanvas-2-wrapper .right-box {
  position: fixed;
  top: 0;
  width: 40%;
  height: 100vh;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
  z-index: 9999;
  padding-bottom: 50px;
}

.tp-offcanvas-2-wrapper .right-box::-webkit-scrollbar {
  display: none;
}

@media (max-width: 767px) {
  .tp-offcanvas-2-wrapper .right-box {
    width: 100%;
  }
}

.tp-offcanvas-2-left {
  visibility: hidden;
  opacity: 0;
  left: 0;
  transition-delay: 1.2s;
}

.tp-offcanvas-2-right {
  visibility: hidden;
  opacity: 0;
  right: 0;
  transition-delay: 1.2s;
}

.tp-offcanvas-2-right-inner {
  padding: 100px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-offcanvas-2-right-inner {
    padding-left: 50px;
    padding-right: 50px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-offcanvas-2-right-inner {
    padding: 50px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-offcanvas-2-right-inner {
    padding: 30px;
  }
}

.tp-offcanvas-2-close {
  padding-top: 30px;
  padding-right: 90px;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: translateY(20px);
  -moz-transform: translateY(20px);
  -ms-transform: translateY(20px);
  -o-transform: translateY(20px);
  transform: translateY(20px);
  transition-delay: 0.9s;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-offcanvas-2-close {
    padding-right: 30px;
  }
}

.tp-offcanvas-2-close-btn {
  color: var(--tp-common-black);
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
}

.tp-offcanvas-2-close-btn .text {
  width: 60px;
  height: 20px;
  overflow: hidden;
  color: var(--tp-common-black);
  display: inline-block;
  transform: translateY(4px);
}

.tp-offcanvas-2-close-btn .text span {
  display: inline-block;
  -webkit-transform: translateX(120%);
  -moz-transform: translateX(120%);
  -ms-transform: translateX(120%);
  -o-transform: translateX(120%);
  transform: translateX(120%);
}

.tp-offcanvas-2-close-btn:hover .text span {
  transform: translateX(0%);
}

.tp-offcanvas-2-close-btn:hover span svg {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

.tp-offcanvas-2-text {
  right: 0;
  bottom: 0;
  position: absolute;
  transform: rotate(-90deg) translateY(100%);
}

.tp-offcanvas-2-text span {
  font-size: 320px;
  font-weight: 700;
  color: rgba(33, 35, 41, 0.3);
  font-family: var(--tp-ff-shoulders);
}

.tpoffcanvas__logo {
  opacity: 0;
  padding-left: 90px;
  padding-top: 35px;
  visibility: hidden;
  transition-delay: 0s;
  -webkit-transform: translateY(30px);
  -moz-transform: translateY(30px);
  -ms-transform: translateY(30px);
  -o-transform: translateY(30px);
  transform: translateY(30px);
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tpoffcanvas__logo {
    padding-left: 30px;
  }
}

.tpoffcanvas__logo img {
  width: 85px;
  height: 100%;
}

.tpoffcanvas__right-wrap {
  height: 100%;
  padding: 100px;
  padding-right: 60px;
}

.tpoffcanvas__right-info {
  opacity: 0;
  text-align: right;
  visibility: hidden;
  transition-delay: 0.6s;
  -webkit-transform: translateY(60px);
  -moz-transform: translateY(60px);
  -ms-transform: translateY(60px);
  -o-transform: translateY(60px);
  transform: translateY(60px);
}

.tpoffcanvas__tel a,
.tpoffcanvas__mail a,
.tpoffcanvas__text p {
  padding: 8px 0px;
  font-weight: 300;
  font-size: 22px;
  line-height: 20px;
  letter-spacing: 0.04em;
  color: var(--tp-common-black);
  display: inline-block;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {

  .tpoffcanvas__tel a,
  .tpoffcanvas__mail a,
  .tpoffcanvas__text p {
    font-size: 18px;
  }
}

.tpoffcanvas__social-link {
  visibility: hidden;
  opacity: 0;
  -webkit-transform: translateY(60px);
  -moz-transform: translateY(60px);
  -ms-transform: translateY(60px);
  -o-transform: translateY(60px);
  transform: translateY(60px);
  transition-delay: 0.9s;
  -webkit-transition: all 1s ease-in-out;
  -moz-transition: all 1s ease-in-out;
  -ms-transition: all 1s ease-in-out;
  -o-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
}

.tpoffcanvas__social-link ul li {
  list-style: none;
}

.tpoffcanvas__social-link ul li:not(:last-child) {
  margin-bottom: 10px;
}

.tpoffcanvas__social-link ul li a {
  display: inline-block;
  font-weight: 300;
  font-size: 20px;
  line-height: 1.3;
  position: relative;
  padding-left: 16px;
  color: var(--tp-common-black);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tpoffcanvas__social-link ul li a {
    font-size: 22px;
  }
}

.tpoffcanvas__social-link ul li a::before {
  position: absolute;
  content: "";
  left: 16px;
  bottom: 3px;
  width: 0;
  height: 1px;
  background-color: var(--tp-common-black);
}

.tpoffcanvas__social-link ul li a:hover::before {
  width: calc(100% - 16px);
}

.tp-offcanvas-gallery-img {
  position: relative;
}

.tp-offcanvas-gallery-img::after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  height: 100%;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.3);
  transition: 0.4s;
}

.tp-offcanvas-gallery-img img {
  transition: 0.4s;
  width: 100%;
}

.tp-offcanvas-gallery-img:hover img {
  transform: scale(1.2);
}

.tp-offcanvas-gallery-img:hover::after {
  opacity: 1;
  visibility: visible;
}


/* HEADER CSS */
/*----------------------------------------*/
/*  3.1 Header Style 1
/*----------------------------------------*/
.container {
  transition: all 1s;
}

.tp-transparent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
}

.header-sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  opacity: 1;
  width: 100%;
  z-index: 999;
  visibility: visible;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 1px 3px 0px rgba(18, 20, 32, 0.14);
  -webkit-animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
  animation: 0.95s ease 0s normal forwards 1 running headerSlideDown;
}

.header-sticky .ab-logo-1 {
  display: none;
}

.header-sticky .ab-logo-2 {
  display: block;
}

.header-sticky::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(10px);
  z-index: -1;
}

.ab-logo-2 {
  display: none;
}

.tp-header-style-9 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.logo-2 {
  display: none;
}

.tp-header-logo img {
  width: 85px;
  height: 100%;
}

.tp-header-area.header-sticky .container {
  max-width: 100%;
}

.tp-header-area.header-sticky .tp-header-menu>nav>ul>li>a {
  padding: 27px 0;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-header-mob-space {
    padding: 20px 0px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-header-area {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-header-area {
    padding-left: 0px;
    padding-right: 0px;
  }
}

.tp-header-menu>nav>ul>li {
  display: inline-block;
  list-style-type: none;
  margin: 0px 25px;
}

.tp-header-menu>nav>ul>li>a {
  padding: 40px 0;
  display: inline-block;
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
  text-transform: uppercase;
  color: var(--tp-common-black);
}

.tp-header-bar {
  line-height: 0;
}

.tp-header-bar button span {
  height: 2px;
  width: 40px;
  background-color: var(--tp-common-black);
  display: block;
  margin: 6px 0;
}

.tp-header-bar button:hover span {
  animation: bar_anim 0.8s cubic-bezier(0.44, 1.1, 0.53, 0.99) 1 forwards;
}

.tp-header-bar button:hover span:nth-child(2) {
  animation-delay: 0.1s;
}

.tp-header-3-area.header-sticky {
  box-shadow: none;
  background: none;
}

.tp-header-3-area.header-sticky::after {
  backdrop-filter: initial;
}

.tp-header-3-area.header-sticky .tp-header-3-logo {
  display: none;
}

.tp-header-3-area.header-sticky .tp-header-3-right {
  display: none !important;
}

.tp-header-3-menu-box {
  padding: 0px 15px;
  padding-right: 30px;
  display: inline-block;
  backdrop-filter: blur(8px);
  background: #fff;
  box-shadow: 0px 4px 4px 0px #00000040;
  border-radius: 8.88px;
  border: 1.78px solid;
  border-image-source: linear-gradient(92.64deg, rgba(255, 255, 255, 0.5) -13.07%, rgba(255, 255, 255, 0) 6.86%, rgba(255, 255, 255, 0) 88.43%, rgba(255, 255, 255, 0.21) 104.39%);
  z-index: -1;

}

/* .menu-bg {
  position: absolute;
  top: 0;
  display: inline-block;
  backdrop-filter: blur(8px);
  background: rgba(46, 46, 46, 0.9);
  border-radius: 100px;
  z-index: -1;
} */

.tp-header-3-bar {
  height: 40px;
  width: 40px;
  line-height: 40px;
  font-size: 18px;
  text-align: center;
  display: inline-block;
  color: var(--tp-common-black);
  border: 1px solid var(--tp-common-black);
  transition: 0.3s;
  margin-left: 15px;
  border-radius: 50%;
}

.tp-header-3-bar:hover {
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
}

.tp-header-3-menu>nav>ul>li {
  list-style: none;
  display: inline-block;
  margin: 0px 15px;
}

.tp-header-3-menu>nav>ul>li>a {
  font-size: 17px;
  font-weight: 500;
  padding: 12px 0;
  transition: 0.3s;
  display: inline-block;
  color: #000;
  font-family: 'Satoshi';
}

.tp-header-3-menu>nav>ul>li>a span {
  margin-left: 5px;
}

.tp-header-3-menu>nav>ul>li:hover {
  color: var(--tp-common-white);
}

.tp-header-3-cart button span {
  margin-left: 7px;
  display: inline-block;
}

.tp-header-3-cart button em {
  position: absolute;
  right: -12px;
  top: -1px;
  color: var(--tp-common-black);
  font-size: 18px;
  font-weight: 400;
  height: 18px;
  width: 18px;
  line-height: 16px;
  border-radius: 50%;
  text-align: center;
  background-color: var(--tp-common-white);
  font-style: normal;
}

.tp-header-3-social a {
  height: 40px;
  width: 40px;
  line-height: 40px;
  border-radius: 50%;
  font-size: 16px;
  text-align: center;
  display: inline-block;
  background-color: var(--tp-grey-2);
  color: var(--tp-common-black);
  margin-left: 5px;
  transition: 0.3s;
}

.tp-header-3-social a:hover {
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
}


.tp-inner-header-style-3 .tp-inner-header-right-action ul::after {
  display: none;
}

.tp-inner-header-menu>nav>ul>li {
  text-decoration: none;
  display: inline-block;
  margin: 0px 25px;
}

.tp-inner-header-menu>nav>ul>li>a {
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  padding: 45px 0px;
  display: inline-block;
  text-transform: uppercase;
  color: var(--tp-common-white);
  font-family: var(--tp-ff-syne);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-inner-header-mob-space {
    padding: 20px 0;
  }
}

.tp-inner-header-right-action ul {
  position: relative;
  margin-left: 30px;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-inner-header-right-action ul {
    margin-left: 0;
  }
}

.tp-inner-header-right-action ul li {
  display: inline-block;
  margin-left: 40px;
}

.tp-inner-header-right-action ul li .tp-inner-cart {
  color: var(--tp-common-white);
}

.tp-inner-header-right-action ul li .tp-inner-cart a span i {
  position: absolute;
  top: -10px;
  right: -13px;
  height: 22px;
  width: 22px;
  line-height: 22px;
  border-radius: 50%;
  text-align: center;
  display: inline-block;
  font-style: normal;
  color: var(--tp-common-black);
  background-color: var(--tp-common-white);
}

.tp-inner-header-right-action ul li .tp-inner-bar {
  color: var(--tp-common-white);
}

.tp-inner-header-border {
  border-bottom: 1px solid rgba(20, 20, 20, 0.1);
}

.tp-inner-header-height .tp-inner-header-menu nav ul li a {
  padding: 32px 0px;
}

.header-sticky.tp-inner-header-area .tp-inner-header-menu>nav>ul>li>a {
  padding: 25px 0px;
  color: var(--tp-common-black);
}

.header-sticky.tp-inner-header-area .tp-inner-header-right-action ul li .tp-inner-cart {
  color: var(--tp-common-black);
}

.header-sticky.tp-inner-header-area .tp-inner-header-right-action ul li .tp-inner-cart a span i {
  color: var(--tp-common-white);
  background-color: var(--tp-common-black);
}

.header-sticky.tp-inner-header-area .tp-inner-header-right-action ul li .tp-inner-bar {
  color: var(--tp-common-black);
}

/*----------------------------------------*/
/*  4.1 Main menu css
/*----------------------------------------*/
.header-main-menu>nav>ul>li>.submenu {
  position: absolute;
  width: 330px;
  z-index: 999;
  padding: 35px 0px;
  top: 100%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  text-align: left;
  margin-left: 0;
  overflow: hidden;
  transform-origin: top;
  transition-duration: 0.1s;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 10px 30px 0px rgba(25, 25, 26, 0.1);
  -webkit-transform: perspective(300px) rotateX(-18deg);
  -moz-transform: perspective(300px) rotateX(-18deg);
  -ms-transform: perspective(300px) rotateX(-18deg);
  -o-transform: perspective(300px) rotateX(-18deg);
  transform: perspective(300px) rotateX(-18deg);
}

.header-main-menu>nav>ul>li>.submenu>li {
  list-style: none;
  display: block;
  padding: 0 45px;
}

.header-main-menu>nav>ul>li>.submenu>li:not(:last-child) {
  margin-bottom: 13px;
}

.header-main-menu>nav>ul>li>.submenu>li>a {
  color: #575758;
  font-size: 15px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.3px;
  text-transform: uppercase;
  position: relative;
}

.header-main-menu>nav>ul>li>.submenu>li>a::before {
  position: absolute;
  top: 10px;
  left: 0;
  content: "";
  height: 2px;
  width: 0px;
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  transition: all 0.3s ease-out 0s;
  background-color: var(--tp-common-black);
}

.header-main-menu>nav>ul>li>.submenu>li:hover>a {
  padding-left: 25px;
  color: var(--tp-common-black);
}

.header-main-menu>nav>ul>li>.submenu>li:hover>a::before {
  width: 20px;
  visibility: visible;
  opacity: 1;
}

.header-main-menu>nav>ul>li>.submenu .submenu {
  left: 100%;
  top: 0;
}

.header-main-menu>nav>ul>li:hover>.submenu {
  visibility: visible;
  opacity: 1;
  transition-duration: 0.2s;
  -webkit-transform: perspective(300px) rotateX(0deg);
  -moz-transform: perspective(300px) rotateX(0deg);
  -ms-transform: perspective(300px) rotateX(0deg);
  -o-transform: perspective(300px) rotateX(0deg);
  transform: perspective(300px) rotateX(0deg);
}

.header-main-menu>nav>ul>li.has-homemenu {
  position: static;
}

.header-main-menu>nav>ul>li.has-homemenu .tp-mega-menu {
  width: 100%;
}

.header-main-menu>nav>ul>li>.tp-mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  padding: 0;
  width: 1170px;
  margin: 0 auto;
}

.header-main-menu>nav>ul>li>.tp-mega-menu .tp-homemenu-wrapper {
  padding: 70px 70px 20px 70px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .header-main-menu>nav>ul>li>.tp-mega-menu .tp-homemenu-wrapper {
    padding: 50px 50px 20px 50px;
  }
}

.header-main-menu>nav>ul>li>.tp-mega-menu .tp-homemenu-wrapper .homemenu {
  margin-bottom: 40px;
}

.header-main-menu>nav>ul>li>.tp-mega-menu .tp-megamenu-portfolio {
  padding: 20px 0px 25px 70px;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px),
only screen and (min-width: 1400px) and (max-width: 1599px),
only screen and (min-width: 1200px) and (max-width: 1399px) {
  .header-main-menu>nav>ul>li>.tp-mega-menu .tp-megamenu-portfolio {
    padding: 20px 0px 25px 20px;
  }
}

.homemenu-title {
  font-size: 15px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.15px;
  color: var(--tp-common-black);
  text-transform: uppercase;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .homemenu-title {
    font-size: 13px;
    letter-spacing: normal;
  }
}

.homemenu-thumb img {
  width: 100%;
  transition: 1s;
}

.homemenu-thumb-wrap {
  padding: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.homemenu-thumb:hover img {
  transform: scale(1.1);
}

.tp-megamenu-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
  display: block;
  padding-bottom: 20px;
  margin-bottom: 30px;
  letter-spacing: -0.32px;
  text-transform: uppercase;
  color: var(--tp-common-black);
  border-bottom: 1px solid rgba(58, 57, 54, 0.1);
  padding-left: 20px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-megamenu-title {
    font-size: 15px;
  }
}

.tp-megamenu-title a {
  padding-left: 20px;
}

.tp-megamenu-list-box {
  padding: 25px 0px 30px 20px;
}

.tp-megamenu-list-wrap ul {
  margin-left: 20px;
  display: inline-block;
}

.tp-megamenu-list-wrap ul li {
  list-style: none;
  width: 50%;
  float: left;
}

.tp-megamenu-list-wrap ul li:not(:last-child) {
  margin-bottom: 15px;
}

.tp-megamenu-list-wrap ul li a {
  color: #575758;
  font-size: 15px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.3px;
  text-transform: uppercase;
  position: relative;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px),
only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-megamenu-list-wrap ul li a {
    font-size: 13px;
    letter-spacing: normal;
  }
}

.tp-megamenu-list-wrap ul li a::before {
  position: absolute;
  top: 10px;
  left: 0;
  content: "";
  height: 2px;
  width: 0px;
  opacity: 0;
  visibility: hidden;
  display: inline-block;
  transition: all 0.3s ease-out 0s;
  background-color: var(--tp-common-black);
}

.tp-megamenu-list-wrap ul li:hover a {
  padding-left: 25px;
  color: var(--tp-common-black);
}

.tp-megamenu-list-wrap ul li:hover a::before {
  width: 20px;
  visibility: visible;
  opacity: 1;
}

.tp-megamenu-list-2 .tp-megamenu-list-wrap ul li {
  float: inherit;
  width: 100%;
}

@media only screen and (min-width: 1600px) and (max-width: 1700px),
only screen and (min-width: 1400px) and (max-width: 1599px),
only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-megamenu-list-2 {
    margin-left: 0;
  }
}

.tp-megamenu-shop-style {
  height: 100%;
}

.tp-megamenu-shop-style .tp-shop-banner-left {
  height: 100%;
}

.tp-megamenu-shop-style .tp-shop-banner-thumb {
  height: 100%;
  overflow: hidden;
}

.tp-megamenu-shop-style .tp-shop-banner-thumb img {
  height: 371px;
  width: 100%;
}

.tp-megamenu-shop-style .tp-shop-banner-content {
  margin: 25px;
}

.tp-megamenu-shop-style .tp-shop-banner-title {
  font-size: 50px;
  margin-bottom: 10px;
}

.tp-megamenu-shop-style .tp-shop-banner-content span {
  font-size: 14px;
  margin-bottom: 18px;
}

.tp-megamenu-shop-style .tp-shop-btn {
  font-size: 13px;
  height: 30px;
  line-height: 29px;
  padding: 0px 18px;
  transition: 0.3s;
}

.tp-megamenu-shop-style .tp-shop-btn:hover {
  background-color: var(--tp-common-white);
  color: var(--tp-common-black);
  border-color: var(--tp-common-white);
}

.tp-megamenu-portfolio-banner {
  position: absolute;
  top: 0;
  right: 0px;
  height: 100%;
}

.tp-megamenu-portfolio-banner img {
  height: 100%;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-megamenu-portfolio-banner {
    right: -130px;
  }
}

.tp-megamenu-wrapper {
  padding: 20px;
}

.tp-megamenu-portfolio-text {
  position: relative;
  z-index: 99;
  bottom: -35px;
  transform: rotate(-90deg) translateY(-110%);
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .tp-megamenu-portfolio-text {
    bottom: -40px;
  }
}

.tp-megamenu-portfolio-text h4 {
  font-size: 160px;
  font-weight: 900;
  line-height: 1;
  margin-bottom: 0;
  letter-spacing: -1.6px;
  -webkit-text-stroke-width: 1px;
  font-family: var(--tp-ff-shoulders);
  -webkit-text-stroke-color: rgba(25, 25, 26, 0.1);
  color: transparent;
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .tp-megamenu-portfolio-text h4 {
    font-size: 120px;
  }
}

.tp-megamenu-portfolio-text span {
  font-size: 30px;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -0.3px;
  color: rgba(25, 25, 26, 0.4);
  font-family: var(--tp-ff-shoulders);
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .tp-megamenu-portfolio-text span {
    font-size: 21px;
  }
}

.tp-main-menu-mobile .tp-submenu {
  display: none;
}

.tp-main-menu-mobile .header-icon {
  display: none;
}

.tp-main-menu-mobile nav ul {
  position: static;
  display: block;
  box-shadow: none;
  margin-bottom: 50px;
}

.tp-main-menu-mobile nav ul li {
  list-style: none;
  position: relative;
  width: 100%;
  padding: 0;
}

.tp-main-menu-mobile nav ul li:not(:last-child)>a {
  border-bottom: 1px solid rgba(6, 7, 40, 0.1);
}

.tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn {
  position: absolute;
  right: 0;
  top: 0;
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  transform: translateY(-2px);
  font-size: 18px;
  color: #7F8387;
  font-family: "Font Awesome 5 Pro";
  transition: all 0.3s ease-in-out;
  text-align: center;
  transition: background-color 0.3s ease-in-out, border-color 0.3s ease-in-out, color 0.3s ease-in-out;
  padding: 15px 20px;
  padding-left: 100px;
}

@media (max-width: 767px) {
  .tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn {
    right: 0;
  }
}

.tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn i {
  transition: all 0.3s ease-in-out;
}

.tp-main-menu-mobile nav ul li.has-dropdown>a .dropdown-toggle-btn.dropdown-opened i {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.tp-main-menu-mobile nav ul li.has-dropdown>a.expanded {
  color: var(--tp-common-black);
}

.tp-main-menu-mobile nav ul li.has-dropdown>a.expanded .dropdown-toggle-btn.dropdown-opened {
  color: var(--tp-common-black);
}

.tp-main-menu-mobile nav ul li.has-dropdown>a.expanded .dropdown-toggle-btn.dropdown-opened i {
  color: var(--tp-common-black);
}

.tp-main-menu-mobile nav ul li:last-child a span {
  border-bottom: 0;
}

.tp-main-menu-mobile nav ul li>a {
  display: block;
  position: relative;
  padding: 15px 0;
  padding-right: 20px;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.15px;
  color: var(--tp-common-black);
  text-transform: uppercase;
}

.tp-main-menu-mobile nav ul li>a svg {
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  transform: translateY(-2px);
}

.tp-main-menu-mobile nav ul li>a>i {
  display: inline-block;
  width: 11%;
  margin-right: 13px;
  -webkit-transform: translateY(4px);
  -moz-transform: translateY(4px);
  -ms-transform: translateY(4px);
  -o-transform: translateY(4px);
  transform: translateY(4px);
  font-size: 21px;
  line-height: 1;
}

.tp-main-menu-mobile nav ul li>a .menu-text {
  font-size: 16px;
  line-height: 11px;
  border-bottom: 1px solid #EAEBED;
  width: 82%;
  display: inline-block;
  padding: 19px 0 17px;
}

.tp-main-menu-mobile nav ul li img {
  width: 100%;
}

.tp-main-menu-mobile nav ul li ul {
  padding: 0;
}

.tp-main-menu-mobile nav ul li ul li {
  padding: 0;
}

.tp-main-menu-mobile nav ul li ul li a {
  margin-left: auto;
  width: 93%;
  padding: 10px 5%;
  text-shadow: none !important;
  visibility: visible;
  padding-left: 0;
  padding-right: 20px;
}

.tp-main-menu-mobile nav ul li ul li li a {
  width: 88%;
  padding: 10px 7%;
  padding-left: 0;
  padding-right: 20px;
}

.tp-main-menu-mobile nav ul li ul li li li a {
  width: 83%;
  padding: 10px 9%;
  padding-left: 0;
  padding-right: 20px;
}

.tp-main-menu-mobile nav ul li ul li li li li a {
  width: 68%;
  padding: 10px 11%;
  padding-left: 0;
  padding-right: 20px;
}

.tp-main-menu-mobile nav ul li:hover .mega-menu {
  visibility: visible;
  opacity: 1;
  top: 0;
}

.tp-main-menu-mobile nav ul li .mega-menu,
.tp-main-menu-mobile nav ul li .submenu {
  position: static;
  min-width: 100%;
  padding: 0;
  box-shadow: none;
  visibility: visible;
  opacity: 1;
  display: none;
}

.tp-main-menu-mobile nav ul li .mega-menu li,
.tp-main-menu-mobile nav ul li .submenu li {
  float: none;
  display: block;
  width: 100%;
  padding: 0;
}

.tp-main-menu-mobile nav ul li .mega-menu li:hover a .dropdown-toggle-btn,
.tp-main-menu-mobile nav ul li .submenu li:hover a .dropdown-toggle-btn {
  color: var(--tp-theme-1);
}

.tp-main-menu-mobile .tp-main-menu-content ul li:not(:last-child) .home-menu-title a {
  border-bottom: none;
}

.tp-main-menu-mobile * ul,
.tp-main-menu-mobile * li {
  transition: none !important;
}

.tp-portfolio-menu-style.tp-megamenu-list-wrap ul li {
  width: 100%;
  float: inherit;
}

/*----------------------------------------*/
/*  5.2 Postbox css
/*----------------------------------------*/
.postbox__thumb {
  position: relative;
}

.postbox__thumb .play-btn {
  position: absolute;
  top: 38%;
  left: 44%;
  z-index: 1;
}

.postbox__thumb .play-btn a {
  height: 85px;
  width: 85px;
  line-height: 87px;
}

.postbox__thumb .play-btn a::after {
  display: none;
}

.postbox__item-single:hover {
  box-shadow: none;
}

.postbox__tag {
  position: absolute;
  bottom: 0px;
  left: 0;
}

.postbox__tag p {
  margin-bottom: 0;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
  text-transform: uppercase;
  color: #000;
  letter-spacing: 0.135em;
  background: var(--tp-theme-1);
  clip-path: polygon(0px 0px, 100% 0px, 92.7% 53.45%, 100% 100%, 0px 100%, 0px 50%);
  width: 130px;
}

.postbox__content {
  padding-top: 28px;
}

.postbox__content-single {
  padding-left: 0;
  padding-right: 0;
  border: none;
}

.postbox__title {
  font-size: 56px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -2.24px;
  color: var(--tp-common-black);
  margin-bottom: 10px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
only screen and (min-width: 576px) and (max-width: 767px) {
  .postbox__title {
    font-size: 30px;
  }
}

@media (max-width: 767px) {
  .postbox__title {
    font-size: 25px;
  }
}

.postbox__title a {
  background-image: -webkit-radial-gradient(#000, #000), -webkit-radial-gradient(#000, #000);
  background-image: -moz-radial-gradient(#000, #000), -moz-radial-gradient(#000, #000);
  background-image: -ms-radial-gradient(#000, #000), -ms-radial-gradient(#000, #000);
  background-image: -o-radial-gradient(#000, #000), -o-radial-gradient(#000, #000);
  background-image: radial-gradient(#000, #000), radial-gradient(#000, #000);
  background-size: 0% 1px, 0 1px;
  background-position: 100% 100%, 0 91%;
  background-repeat: no-repeat;
}

.postbox__title a:hover {
  background-size: 0 1px, 100% 1px;
}

.postbox__meta {
  margin-bottom: 5px;
}

.postbox__meta span {
  color: #414145;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  text-transform: uppercase;
}

.postbox__blockquote {
  background: #F4F6F8;
  padding: 50px 65px;
}

@media (max-width: 767px) {
  .postbox__blockquote {
    padding: 20px;
  }
}

.postbox__blockquote-icon {
  margin-bottom: 20px;
  display: inline-block;
}

.postbox__blockquote p {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  text-transform: uppercase;
  color: var(--tp-common-black);
  margin-bottom: 15px;
}

@media (max-width: 767px) {
  .postbox__blockquote p {
    font-size: 19px;
    font-weight: 28px;
  }
}

.postbox__blockquote-info {
  color: #5D5D63;
  font-size: 14px;
  font-weight: 600;
  line-height: 32px;
}

.postbox-details-desc-thumb-caption {
  font-size: 18px;
  font-weight: 500;
  color: #807A7A;
  font-style: italic;
  text-align: center;
  display: block;
  margin-top: 30px;
  margin-bottom: 50px;
}

.postbox__list {
  margin-bottom: 60px;
}

.postbox__list-title {
  font-weight: 600;
  font-size: 28px;
  color: #121D2C;
  margin-bottom: 50px;
}

.postbox__list-content ul li {
  list-style: none;
  font-weight: 400;
  font-size: 16px;
  color: #445658;
  margin-bottom: 24px;
  position: relative;
  padding-left: 40px;
}

.postbox__list-content ul li span {
  height: 27px;
  width: 27px;
  line-height: 25px;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  background-color: var(--tp-common-white);
  color: var(--tp-theme-1);
  box-shadow: 0px 0px 9px rgba(0, 0, 0, 0.06);
  position: absolute;
  top: 0;
  left: 0;
}

.postbox__list-content ul li span.active {
  background-color: var(--tp-theme-1);
  color: var(--tp-common-white);
}

.postbox__details-share-wrapper {
  padding-top: 20px;
  padding-bottom: 30px;
  border-bottom: 1px solid #F7F7F7;
}

.postbox__details-share span {
  font-size: 20px;
  font-weight: 500;
  color: #121416;
  margin-right: 15px;
}

.postbox__details-share a {
  height: 37px;
  width: 37px;
  text-align: center;
  line-height: 37px;
  display: inline-block;
  background-color: #F2F6F7;
  color: var(--tp-common-black);
  margin-right: 10px;
  transition: all 0.3s ease-in-out;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .postbox__details-share a {
    margin-bottom: 10px;
    height: 30px;
    width: 30px;
    line-height: 30px;
    margin-right: 8px;
  }
}

.postbox__details-share a:hover {
  background-color: var(--tp-theme-1);
  color: var(--tp-common-white);
}

.postbox__details-tag span {
  font-size: 20px;
  font-weight: 500;
  color: #121416;
  margin-right: 6px;
}

.postbox__read-more .postbox-btn {
  position: relative;
  font-weight: 500;
  font-size: 14px;
  text-transform: uppercase;
  color: #132047;
}

.postbox__read-more .postbox-btn span {
  margin-left: 5px;
}

.postbox__read-more .postbox-btn span::before {
  position: absolute;
  content: "";
  top: 1px;
  right: -10px;
  border: 1px solid #E8F2F9;
  height: 28px;
  width: 28px;
  display: inline-block;
  border-radius: 50%;
}

.postbox__text img {
  max-width: 100%;
}

.postbox__text p {
  color: #5D5D63;
  font-size: 17px;
  font-weight: 400;
  line-height: 26px;
  margin-bottom: 30px;
}

.postbox__text-single p {
  margin-bottom: 15px;
}

.postbox__slider button {
  position: absolute;
  left: 50px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 1;
  font-size: 30px;
  color: var(--tp-common-white);
}

.postbox__slider button.postbox-slider-button-next {
  left: auto;
  right: 50px;
}

@media (max-width: 767px) {
  .postbox__slider button.postbox-slider-button-next {
    right: 10px;
  }
}

@media (max-width: 767px) {
  .postbox__slider button {
    left: 10px;
  }
}

.postbox__comment ul li {
  margin-bottom: 45px;
  list-style: none;
}

.postbox__comment ul li.children {
  margin-left: 65px;
}

@media (max-width: 767px) {
  .postbox__comment ul li.children {
    margin-left: 15px;
  }
}

.postbox__comment-form {
  margin-bottom: 40px;
  padding: 65px 45px 80px 45px;
  background: #F8F8F9;
}

@media (max-width: 767px) {
  .postbox__comment-form {
    padding: 20px;
  }
}

.postbox__comment-form-title {
  font-size: 26px;
  font-weight: 600;
  margin-bottom: 40px;
}

.postbox__comment-input {
  position: relative;
  margin-bottom: 20px;
}

.postbox__comment-input span {
  font-weight: 600;
  color: var(--tp-common-black);
  margin-bottom: 12px;
  display: block;
}

.postbox__comment-input input,
.postbox__comment-input textarea {
  height: 55px;
  padding: 0 20px;
  width: 100%;
  font-size: 14px;
  color: var(--tp-common-black);
  outline: none;
  background: #FFFFFF;
  border: 1px solid #E5E5E5;
  box-shadow: 0px 15px 10px rgba(242, 242, 242, 0.18);
}

.postbox__comment-input input:focus,
.postbox__comment-input textarea:focus {
  border: 1px solid var(--tp-theme-1);
}

.postbox__comment-input input:focus::placeholder,
.postbox__comment-input textarea:focus::placeholder {
  font-size: 0;
}

.postbox__comment-input textarea {
  height: 175px;
  resize: none;
  padding-top: 20px;
  padding-bottom: 20px;
}

.postbox__comment-title {
  font-size: 28px;
  color: #121416;
  font-weight: 600;
  margin-bottom: 20px;
}

.postbox__comment-box {
  padding-top: 20px;
}

@media (max-width: 767px) {
  .postbox__comment-box {
    flex-wrap: wrap;
  }
}

.postbox__comment-info {
  flex: 0 0 auto;
}

@media (max-width: 767px) {
  .postbox__comment-avater {
    margin-right: 0;
  }
}

.postbox__comment-avater img {
  width: 90px;
  height: 90px;
}

@media (max-width: 767px) {
  .postbox__comment-avater img {
    margin-bottom: 30px;
  }
}

.postbox__comment-name {
  margin-bottom: 17px;
}

.postbox__comment-name h5 {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -0.4px;
  color: var(--tp-common-black);
  margin-bottom: 0;
}

.postbox__comment-name span {
  color: #414144;
  font-size: 17px;
  font-weight: 400;
  line-height: 1;
}

.postbox__comment-text p {
  color: #5D5D63;
  font-size: 17px;
  font-weight: 400;
  line-height: 26px;
}

.postbox__comment-reply a {
  color: #19191A;
  font-size: 15px;
  font-weight: 600;
  line-height: 1;
  padding: 5px 20px;
  border: 1px solid #E0E2E3;
}

.postbox__comment-reply a:hover {
  background-color: var(--tp-common-black);
  color: var(--tp-common-white);
  border-color: var(--tp-common-white);
}

.postbox__comment-agree {
  padding-left: 5px;
}

.postbox__comment-agree input {
  margin: 0;
  appearance: none;
  -moz-appearance: none;
  display: block;
  width: 16px;
  height: 16px;
  background: var(--tp-common-white);
  border: 1px solid #949392;
  outline: none;
  flex: 0 0 auto;
  -webkit-transform: translateY(-1px);
  -moz-transform: translateY(-1px);
  -ms-transform: translateY(-1px);
  -o-transform: translateY(-1px);
  transform: translateY(-1px);
}

.postbox__comment-agree input:checked {
  position: relative;
  background-color: var(--tp-theme-1);
  border-color: transparent;
}

.postbox__comment-agree input:checked::after {
  box-sizing: border-box;
  content: "\f00c";
  position: absolute;
  font-family: var(--tp-ff-fontawesome);
  font-size: 10px;
  color: var(--tp-common-white);
  top: 47%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.postbox__comment-agree input:hover {
  cursor: pointer;
}

.postbox__comment-agree label {
  padding-left: 8px;
  color: #838383;
  line-height: 1;
}

.postbox__comment-agree label a {
  color: var(--tp-common-black);
  font-weight: 600;
  padding-left: 4px;
}

.postbox__comment-agree label a:hover {
  color: var(--tp-theme-1);
}

.postbox__comment-agree label:hover {
  cursor: pointer;
}

.postbox__tag span {
  font-size: 16px;
  margin-bottom: 17px;
  color: var(--tp-common-black);
  margin-right: 10px;
}

.postbox__play-btn a {
  height: 66px;
  width: 66px;
  line-height: 66px;
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  border-radius: 50%;
  animation: pulse 2s infinite;
  transform: translate(-50%, -50%);
  color: var(--tp-common-white);
  border: 2px solid var(--tp-common-white);
}

@media (max-width: 767px) {
  .postbox__area {
    padding-top: 60px;
  }
}

.postbox__link-post-wrap {
  background-color: #F9F9F9;
  padding: 60px;
}

@media (max-width: 767px) {
  .postbox__link-post-wrap {
    padding: 20px;
    flex-wrap: wrap;
  }
}

.postbox__link-post-wrap p {
  margin-bottom: 0;
  font-size: 24px;
  font-weight: 600;
  line-height: 34px;
  color: var(--tp-common-black);
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
  .postbox__link-post-wrap p br {
    display: none;
  }
}

@media (max-width: 767px) {
  .postbox__link-post-wrap p {
    font-size: 19px;
    font-weight: 28px;
  }

  .postbox__link-post-wrap p br {
    display: none;
  }
}

.postbox__link-post-icon {
  margin-right: 40px;
  display: inline-block;
}

@media (max-width: 767px) {
  .postbox__link-post-icon {
    margin-bottom: 10px;
    margin-right: 0;
  }
}

.postbox__slider-arrow-wrap button {
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 18px;
  border-radius: 50%;
  color: var(--tp-common-white);
  border: 1px solid var(--tp-common-white);
  transition: 0.3s;
}

.postbox__slider-arrow-wrap button:hover {
  background-color: var(--tp-common-white);
  color: var(--tp-common-black);
}

.postbox__slider-arrow-wrap .postbox-arrow-prev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 30px;
  z-index: 9;
}

.postbox__slider-arrow-wrap .postbox-arrow-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 30px;
  z-index: 9;
}

.basic-pagination {
  margin-bottom: 40px;
}

.basic-pagination ul li {
  list-style: none;
  display: inline-block;
  margin-right: 14px;
}

@media (max-width: 767px) {
  .basic-pagination ul li {
    margin-right: 5px;
  }
}

.basic-pagination ul li a {
  height: 46px;
  width: 30px;
  border-radius: 200px;
  display: inline-block;
  line-height: 40px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  transition: all 0.3s ease-in-out;
  color: var(--tp-common-black);
}

.basic-pagination ul li a .current {
  height: 100%;
  width: 100%;
  display: inline-block;
  border-radius: 200px;
  border: 2px solid var(--tp-common-black);
}

.basic-pagination ul li a .icon {
  height: 46px;
  width: 46px;
  line-height: 46px;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  border-radius: 100px;
  border: 1px solid rgba(25, 25, 26, 0.2);
  transition: 0.3s;
}

.basic-pagination ul li a .icon:hover {
  color: var(--tp-common-white);
  border-color: var(--tp-common-black);
  background-color: var(--tp-common-black);
}

.tp-postbox-details-remeber {
  display: flex;
  align-items: start;
}

.tp-postbox-details-form p {
  color: #5D5D63;
  font-size: 17px;
  font-weight: 400;
  line-height: 26px;
}

@media (max-width: 767px) {
  .tp-postbox-details-form p {
    font-size: 16px;
  }
}

.tp-postbox-details-form-title {
  color: #19191A;
  font-size: 34px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -0.68px;
}

.tp-postbox-details-input input {
  height: 50px;
  line-height: 50px;
  border: none;
  border: 1px solid rgba(25, 25, 26, 0.1);
  font-size: 17px;
  font-weight: 400;
  color: var(--tp-common-black);
  margin-bottom: 20px;
}

.tp-postbox-details-input input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input input::-moz-placeholder {
  /* Firefox 19+ */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input input:-moz-placeholder {
  /* Firefox 4-18 */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input input::placeholder {
  /* MODERN BROWSER */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input input::focus {
  border-color: var(--tp-common-black);
}

.tp-postbox-details-input textarea {
  height: 160px;
  font-size: 17px;
  font-weight: 400;
  resize: none;
  margin-bottom: 20px;
  color: var(--tp-common-black);
}

.tp-postbox-details-input textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input textarea::-moz-placeholder {
  /* Firefox 19+ */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input textarea:-moz-placeholder {
  /* Firefox 4-18 */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input textarea::placeholder {
  /* MODERN BROWSER */
  font-size: 17px;
  font-weight: 400;
  color: #707072;
}

.tp-postbox-details-input textarea::focus {
  border-color: var(--tp-common-black);
}

.tp-postbox-details-remeber input {
  margin-right: 10px;
  transform: translateY(7px);
}

.tp-postbox-details-remeber label {
  color: #5D5D63;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  cursor: pointer;
}

/*----------------------------------------*/
/*  5.3 Recent Post css
/*----------------------------------------*/
.rc__post ul li:not(:last-child) {
  margin-bottom: 15px;
}

.rc__post-title {
  font-size: 17px;
  font-weight: 600;
  line-height: 22px;
  color: var(--tp-common-black);
}

.rc__post-title a {
  background-image: -webkit-radial-gradient(#000, #000), -webkit-radial-gradient(#000, #000);
  background-image: -moz-radial-gradient(#000, #000), -moz-radial-gradient(#000, #000);
  background-image: -ms-radial-gradient(#000, #000), -ms-radial-gradient(#000, #000);
  background-image: -o-radial-gradient(#000, #000), -o-radial-gradient(#000, #000);
  background-image: radial-gradient(#000, #000), radial-gradient(#000, #000);
  background-size: 0% 1px, 0 1px;
  background-position: 100% 100%, 0 91%;
  background-repeat: no-repeat;
}

.rc__post-title a:hover {
  background-size: 0 1px, 100% 1px;
}

.rc__meta span {
  color: #414145;
  font-size: 15px;
  font-weight: 400;
  line-height: 1;
  margin-bottom: 15px;
  display: inline-block;
}

.rc__post-thumb {
  flex: 0 0 auto;
}

/*----------------------------------------*/
/*  5.4 Sidebar css
/*----------------------------------------*/
.sidebar__wrapper {
  padding-left: 70px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .sidebar__wrapper {
    padding-left: 0;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .sidebar__wrapper {
    margin-top: 40px;
    padding-left: 0;
  }
}

.sidebar__widget-title {
  font-size: 22px;
  font-weight: 700;
  line-height: 1;
  text-transform: uppercase;
  color: var(--tp-common-black);
  margin-bottom: 25px;
}

.sidebar__widget ul li {
  list-style: none;
  margin-bottom: 20px;
  line-height: 0;
}

.sidebar__widget ul li:last-child {
  margin-bottom: 0;
}

.sidebar__widget ul li a {
  color: #414145;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  background-image: -webkit-radial-gradient(#414145, #414145), -webkit-radial-gradient(#414145, #414145);
  background-image: -moz-radial-gradient(#414145, #414145), -moz-radial-gradient(#414145, #414145);
  background-image: -ms-radial-gradient(#414145, #414145), -ms-radial-gradient(#414145, #414145);
  background-image: -o-radial-gradient(#414145, #414145), -o-radial-gradient(#414145, #414145);
  background-image: radial-gradient(#414145, #414145), radial-gradient(#414145, #414145);
  background-size: 0% 1px, 0 1px;
  background-position: 100% 100%, 0 91%;
  background-repeat: no-repeat;
}

.sidebar__widget ul li a:hover {
  background-size: 0 1px, 100% 1px;
}

.sidebar__about {
  padding: 37px 0 38px 0;
}

.sidebar__thumb img {
  border-radius: 50%;
  margin-bottom: 20px;
}

.sidebar__content-title {
  font-weight: 700;
  font-size: 16px;
  text-transform: uppercase;
  color: var(--tp-common-black);
  margin-bottom: 6px;
}

.sidebar__content-designation {
  font-weight: 400;
  font-size: 14px;
  color: #727A7D;
  display: block;
  margin-bottom: 13px;
}

.sidebar__content p {
  font-size: 16px;
  color: #838383;
  margin-bottom: 27px;
}

.sidebar__content-social a {
  height: 37px;
  width: 42px;
  line-height: 37px;
  text-align: center;
  display: inline-block;
  border: 1px solid #E8E8E8;
  margin-right: 12px;
}

.sidebar__content-social a i {
  transition: 0.3s;
}

.sidebar__content-social a:hover {
  border: 1px solid var(--tp-theme-1);
  background-color: var(--tp-theme-1);
}

.sidebar__content-social a:hover i {
  color: var(--tp-common-white);
}

.sidebar__search {
  position: relative;
}

.sidebar__search input {
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 0 25px;
  outline: none;
  border: none;
  padding-right: 50px;
  background: transparent;
  border: 1px solid #D9D9D9;
}

.sidebar__search input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #5D5D63;
  font-size: 18px;
  font-weight: 400;
  line-height: 1;
}

.sidebar__search input::-moz-placeholder {
  /* Firefox 19+ */
  color: #5D5D63;
  font-size: 18px;
  font-weight: 400;
  line-height: 1;
}

.sidebar__search input:-moz-placeholder {
  /* Firefox 4-18 */
  color: #5D5D63;
  font-size: 18px;
  font-weight: 400;
  line-height: 1;
}

.sidebar__search input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #5D5D63;
  font-size: 18px;
  font-weight: 400;
  line-height: 1;
}

.sidebar__search input::placeholder {
  /* MODERN BROWSER */
  color: #5D5D63;
  font-size: 18px;
  font-weight: 400;
  line-height: 1;
}

.sidebar__search button {
  position: absolute;
  top: 0;
  right: 25px;
  height: 100%;
  line-height: 60px;
  color: var(--tp-common-black);
}

.sidebar__banner::after {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(65, 65, 69, 0.5);
}

.sidebar__banner-content {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 1;
  background: var(--tp-common-white);
}

.sidebar__banner-content h4 {
  padding: 15px 20px;
  font-size: 24px;
  color: var(--tp-common-black);
  text-transform: uppercase;
  margin-bottom: 0;
}

.sidebar__author {
  border: 1px solid #19191A;
  padding: 50px 30px;
}

.sidebar__author-thumb img {
  height: 120px;
  width: 120px;
  border-radius: 50%;
  margin-bottom: 30px;
}

.sidebar__author-title {
  font-size: 22px;
  font-weight: 700;
  line-height: 1;
  color: var(--tp-common-black);
  margin-bottom: 10px;
}

.sidebar__author-content p {
  color: #5D5D63;
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  margin-bottom: 0;
}

.sidebar__social a {
  height: 45px;
  width: 45px;
  border-radius: 50%;
  text-align: center;
  line-height: 45px;
  display: inline-block;
  color: var(--tp-common-black);
  border: 1px solid rgba(25, 25, 26, 0.14);
  margin-right: 8px;
  font-size: 18px;
}

.sidebar__social a:hover {
  background-color: var(--tp-common-black);
  border-color: var(--tp-common-black);
  color: var(--tp-common-white);
}

@media (max-width: 767px) {
  .sidebar__banner-img img {
    width: 100%;
  }
}

.tagcloud a {
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  padding: 6px 15px;
  margin-right: 5px;
  margin-bottom: 14px;
  display: inline-block;
  text-transform: uppercase;
  color: var(--tp-common-black);
  border: 1px solid rgba(25, 25, 26, 0.1);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tagcloud a {
    padding: 6px 12px;
  }
}

.tagcloud a:hover {
  color: var(--tp-common-white);
  background-color: var(--tp-common-black);
  border-color: var(--tp-common-black);
}

.blog-sidebar-slider-bg {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.blog-sidebar-slider-height {
  height: 930px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-sidebar-slider-height {
    padding-top: 100px;
    padding-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .blog-sidebar-slider-height {
    height: 700px;
  }
}

.blog-sidebar-slider-title {
  font-size: 80px;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -4px;
  color: var(--tp-common-white);
  margin-bottom: 45px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-sidebar-slider-title {
    font-size: 75px;
  }
}

@media (max-width: 767px) {
  .blog-sidebar-slider-title {
    font-size: 52px;
  }
}

.blog-sidebar-slider-link {
  font-size: 18px;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -0.36px;
  color: var(--tp-common-white);
  position: relative;
  margin-left: 20px;
}

.blog-sidebar-slider-link:hover {
  color: var(--tp-common-white);
}

.blog-sidebar-slider-link::after {
  content: "";
  height: 1px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.4);
  position: absolute;
  bottom: -4px;
  left: 0;
}

.blog-sidebar-slider-link::before {
  content: "";
  height: 1px;
  width: 0;
  position: absolute;
  bottom: -4px;
  right: 0;
  background-color: #fff;
  transition: 0.4s;
}

.blog-sidebar-slider-link:hover::before {
  width: 100%;
  left: 0;
  right: auto;
}

.blog-sidebar-slider-meta {
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  text-transform: uppercase;
  color: var(--tp-common-white);
  margin-bottom: 25px;
  display: inline-block;
}

.blog-sidebar-content-box {
  height: 100%;
  padding-left: 100px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .blog-sidebar-content-box {
    padding-left: 0;
  }
}

.blog-sidebar-avatar-box {
  padding-bottom: 150px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-sidebar-avatar-box {
    padding-bottom: 120px;
  }
}

@media (max-width: 767px) {
  .blog-sidebar-avatar-box {
    padding-bottom: 60px;
  }
}

.blog-sidebar-avatar-box span {
  font-size: 18px;
  font-weight: 700;
  line-height: 1;
  color: var(--tp-common-white);
}

.blog-sidebar-avatar-box img {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  margin-right: 15px;
}

.blog-sidebar-arrow-box {
  position: absolute;
  top: 55%;
  right: 22%;
  z-index: 9;
  transform: translateY(-50%);
}

.blog-sidebar-arrow-box button {
  height: 100px;
  width: 100px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 50%;
  line-height: 1;
  backdrop-filter: blur(7px);
  color: var(--tp-common-white);
  background-color: rgba(255, 255, 255, 0.14);
}

.blog-sidebar-scrollbar {
  position: absolute;
  bottom: 130px;
  right: 50px;
  z-index: 99;
}

@media (max-width: 767px) {
  .blog-sidebar-scrollbar {
    bottom: 40px;
    right: auto;
    left: 15px;
  }
}

.blog-sidebar-scrollbar a {
  font-size: 18px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.36px;
  color: rgba(255, 255, 255, 0.7);
}

.blog-sidebar-scrollbar a span svg {
  margin-left: 20px;
  display: inline-block;
  color: var(--tp-common-white);
  animation: scroll-up-down 1s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite alternate;
  animation-delay: 0s;
  animation-delay: 0s;
  animation-delay: 0s;
  -webkit-animation-delay: 0.75s;
  animation-delay: 0.75s;
  margin-top: -25px;
}

/*----------------------------------------*/
/*  5.1 blog css start
/*----------------------------------------*/
.tp-blog-meta {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  text-align: right;
  margin: 25px;
}

.tp-blog-meta span {
  font-size: 16px;
  font-weight: 500;
  height: 32px;
  line-height: 32px;
  padding: 0px 14px;
  color: var(--tp-common-black);
  background-color: var(--tp-common-white);
  display: inline-block;
}

.tp-blog-content span {
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  color: #5D5D63;
  margin-bottom: 10px;
  display: inline-block;
}

.tp-blog-title-sm {
  font-size: 31px;
  font-weight: 600;
  line-height: 0.8;
  letter-spacing: -0.62px;
  color: var(--tp-common-black);
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-blog-title-sm {
    font-size: 20px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-blog-title-sm {
    font-size: 24px;
  }
}

.tp-blog-title-sm a {
  background-image: linear-gradient(#000, #000), linear-gradient(#000, #000);
  background-size: 0% 1px, 0 1px;
  background-position: 100% 100%, 0 100%;
  background-repeat: no-repeat;
  transition: background-size 0.3s linear;
}

.tp-blog-title-sm:hover a {
  background-size: 0% 1px, 100% 1px;
}

.tp-blog-thumb {
  margin-bottom: 25px;
}

.tp-blog-thumb img {
  width: 100%;
  transition: 0.9s;
}

.tp-blog-item:hover .tp-blog-thumb img {
  transform: scale(1.2) rotate(-2deg);
}

.tp-blog-6-meta {
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  color: var(--tp-common-white);
  margin-bottom: 15px;
  display: inline-block;
}

.tp-blog-6-title-sm {
  font-size: 34px;
  font-weight: 400;
  line-height: 1.1;
  text-transform: uppercase;
  color: var(--tp-common-white);
  font-family: var(--tp-ff-gallery);
  margin-bottom: 15px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-blog-6-title-sm {
    font-size: 25px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-blog-6-title-sm {
    font-size: 24px;
  }
}

@media (max-width: 767px) {
  .tp-blog-6-title-sm {
    font-size: 27px;
  }
}

.tp-blog-6-link a {
  font-size: 15px;
  font-weight: 600;
  line-height: 24px;
  text-transform: uppercase;
  color: var(--tp-common-white);
}

.tp-blog-6-link a i {
  margin-left: 10px;
}

.tp-blog-6-item {
  position: relative;
}

.tp-blog-6-item::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(28, 26, 32, 0.62);
  content: "";
}

.tp-blog-6-item:hover .tp-blog-6-content {
  transform: translateY(0);
}

.tp-blog-6-content-wrap {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 55;
  margin: 40px;
  overflow: hidden;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-blog-6-content-wrap {
    margin: 25px;
  }
}

.tp-blog-6-thumb img {
  width: 100%;
}

.tp-blog-6-content {
  transform: translateY(45px);
  transition: 0.3s;
}

.tp-blog-6-category {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 55;
  margin: 25px;
}

.tp-blog-6-category span {
  font-size: 13px;
  font-weight: 600;
  display: inline-block;
  text-transform: uppercase;
  backdrop-filter: blur(10px);
  color: var(--tp-common-white);
  background: rgba(255, 255, 255, 0.24);
  padding: 0px 10px;
}

@media (max-width: 767px) {
  .blog-details-bg {
    padding-bottom: 140px;
  }
}

.blog-details-bg-height {
  height: 800px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-details-bg-height {
    height: 700px;
  }
}

@media (max-width: 767px) {
  .blog-details-bg-height {
    height: 600px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .blog-details-bg-height {
    height: 700px;
  }
}

.blog-details-overlay {
  position: relative;
}

.blog-details-overlay::after {
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  content: "";
  position: absolute;
  background: rgba(0, 0, 0, 0.36);
}

.blog-details-overlay-shape {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.blog-details-overlay-shape img {
  height: 100%;
}

.blog-details-meta {
  font-size: 17px;
  font-weight: 500;
  line-height: 1;
  text-transform: uppercase;
  color: var(--tp-common-white);
  margin-bottom: 10px;
  display: inline-block;
}

.blog-details-meta i {
  font-size: 17px;
  font-weight: 500;
  line-height: 20px;
  font-style: normal;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.7);
}

.blog-details-title {
  font-size: 100px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -4px;
  margin-bottom: 30px;
  color: var(--tp-common-white);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-details-title {
    font-size: 85px;
  }
}

@media (max-width: 767px) {
  .blog-details-title {
    font-size: 60px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .blog-details-title {
    font-size: 80px;
  }
}

.blog-details-top-author img {
  height: 60px;
  width: 60px;
  border-radius: 50%;
  margin-right: 20px;
}

.blog-details-top-author span {
  font-size: 20px;
  font-weight: 500;
  line-height: 1;
  color: var(--tp-common-white);
}

.blog-details-top-author span i {
  font-size: 20px;
  font-weight: 500;
  line-height: 1;
  font-style: normal;
  color: rgba(255, 255, 255, 0.6);
}

.blog-details-top-text p {
  font-size: 24px;
  font-weight: 400;
  line-height: 34px;
  color: var(--tp-common-black);
  margin-bottom: 50px;
}

.blog-details-left-title {
  font-size: 30px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -0.6px;
  color: var(--tp-common-black);
  margin-bottom: 20px;
}

.blog-details-left-content {
  margin-bottom: 70px;
}

.blog-details-left-content p {
  color: #5D5D63;
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
}

.blog-details-left-content p i {
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
  color: var(--tp-common-black);
}

.blog-details-thumb-box {
  margin-bottom: 75px;
}

.blog-details-blockquote {
  margin-left: 100px;
  position: relative;
  margin-bottom: 50px;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-details-blockquote {
    margin-left: 40px;
  }
}

@media (max-width: 767px) {
  .blog-details-blockquote {
    margin-left: 30px;
  }
}

.blog-details-blockquote p {
  font-size: 44px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: -0.88px;
  color: var(--tp-common-black);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-details-blockquote p {
    font-size: 36px;
  }
}

@media (max-width: 767px) {
  .blog-details-blockquote p {
    font-size: 30px;
  }
}

.blog-details-blockquote .quote-icon {
  position: absolute;
  top: -30px;
  left: -35px;
}

.blog-details-blockquote .blockquote-info {
  color: #5D5D63;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.32px;
  text-transform: uppercase;
}

.blog-details-tag a,
.blog-details-share a {
  font-size: 15px;
  font-weight: 600;
  line-height: 1;
  text-transform: uppercase;
  color: var(--tp-common-black);
  margin-left: 20px;
  position: relative;
}

.blog-details-tag a::after,
.blog-details-share a::after {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 1px;
  content: "";
  transition: 0.4s;
  background-color: var(--tp-common-black);
}

.blog-details-tag a:hover::after,
.blog-details-share a:hover::after {
  right: auto;
  left: 0;
  width: 100%;
}

@media (max-width: 767px) {

  .blog-details-tag a,
  .blog-details-share a {
    margin-left: 10px;
  }
}

.blog-details-tag span,
.blog-details-share span {
  transform: translateY(-2px);
  display: inline-block;
}

.blog-details-author {
  background-color: #F7F7F7;
}

@media (max-width: 767px) {
  .blog-details-author {
    flex-wrap: wrap;
  }
}

.blog-details-author-title {
  font-size: 26px;
  font-weight: 600;
  line-height: 1;
  color: var(--tp-common-black);
  margin-bottom: 15px;
}

.blog-details-author-content-wrap {
  padding: 40px;
}

@media (max-width: 767px) {
  .blog-details-author-content-wrap {
    padding: 20px;
  }
}

.blog-details-author-content p {
  color: #5D5D63;
  font-size: 17px;
  font-weight: 400;
  line-height: 26px;
  margin-bottom: 0;
}

.blog-details-author-img {
  flex: 0 0 auto;
}

@media (max-width: 767px) {
  .blog-details-author-img {
    width: 100%;
  }
}

.blog-details-author-img img {
  height: 100%;
  width: 100%;
}

.blog-details-author-social a {
  color: var(--tp-common-black);
  margin-left: 20px;
}

.blog-details-navigation-style .project-details-1-navigation {
  border-top: 1px transparent;
  padding: 0;
  padding-top: 0;
}

.blog-details-realated-title {
  font-size: 50px;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -2px;
  color: var(--tp-common-black);
}

.blog-details-thumb-wrap {
  border-top: 1px solid #D9D9D9;
}

.blog-details-top-meta {
  padding: 55px 0;
}

@media (max-width: 767px) {
  .blog-details-top-meta {
    padding: 20px 0;
  }
}

.blog-details-top-meta span {
  color: #414145;
  font-size: 17px;
  font-weight: 500;
  line-height: 1;
  text-transform: uppercase;
  display: inline-block;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-blog-standard-area {
    padding-top: 120px;
  }
}

@media (max-width: 767px) {
  .tp-blog-standard-area {
    padding-top: 100px;
  }
}

.tp-blog-standard-title {
  font-size: 60px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -2.4px;
  color: var(--tp-common-white);
  margin-bottom: 0;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-blog-standard-title {
    font-size: 40px;
  }
}

@media (max-width: 767px) {
  .tp-blog-standard-title {
    font-size: 30px;
  }

  .tp-blog-standard-title br {
    display: none;
  }
}

.tp-blog-standard-title-box {
  background-color: var(--tp-common-black);
  display: inline-block;
  padding: 45px;
  position: absolute;
  bottom: 30px;
  left: 30px;
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tp-blog-standard-title-box {
    bottom: 0;
    left: 0;
  }
}

.tp-blog-standard-meta {
  position: absolute;
  top: 30px;
  left: 30px;
}

.tp-blog-standard-meta span {
  padding: 15px;
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
  display: inline-block;
  text-align: center;
  color: var(--tp-common-white);
  background-color: var(--tp-common-black);
}

.tp-blog-standard-thumb-box {
  overflow: hidden;
  height: 750px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-blog-standard-thumb-box {
    height: 600px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-blog-standard-thumb-box {
    height: 470px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-blog-standard-thumb-box {
    height: 335px;
  }
}

@media (max-width: 767px) {
  .tp-blog-standard-thumb-box {
    height: 135px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .tp-blog-standard-thumb-box {
    height: 270px;
  }
}

.tp-blog-standard-thumb-box img {
  margin-top: -180px;
}

@media (max-width: 767px) {
  .tp-blog-standard-thumb-box img {
    margin-top: -120px;
  }
}

.tp-blog-list-bg {
  padding-top: 230px;
  padding-bottom: 430px;
  background-repeat: no-repeat;
  background-size: cover;
}

.tp-blog-list-bg-overlay {
  position: relative;
}

.tp-blog-list-bg-overlay::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: rgba(0, 0, 0, 0.3);
}

.tp-blog-list-text span {
  font-size: 24px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.96px;
  display: inline-block;
  color: var(--tp-common-white);
  transform: translateY(-20px);
}

@media (max-width: 767px) {
  .tp-blog-list-text span {
    transform: translateY(0px);
  }
}

.tp-blog-list-content {
  height: 100%;
}

.tp-blog-list-content-wrap {
  height: 100%;
}

.tp-blog-list-meta span {
  color: #5D5D63;
  font-size: 20px;
  font-weight: 400;
  line-height: 1;
  text-transform: uppercase;
  display: inline-block;
}

@media only screen and (min-width: 768px) and (max-width: 991px),
(max-width: 767px) {
  .tp-blog-list-meta span {
    margin-bottom: 20px;
  }
}

.tp-blog-list-thumb {
  margin-right: 80px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-blog-list-thumb {
    margin-right: 30px;
  }
}

@media (max-width: 767px) {
  .tp-blog-list-thumb {
    margin-right: 0;
    margin-bottom: 20px;
  }
}

.tp-blog-list-thumb img {
  flex: 0 0 auto;
  width: 100%;
}

.tp-blog-list-title-sm {
  font-size: 34px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.68px;
  color: var(--tp-common-black);
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .tp-blog-list-title-sm {
    font-size: 30px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-blog-list-title-sm {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .tp-blog-list-title-sm {
    font-size: 30px;
  }
}

.tp-blog-list-link {
  font-size: 18px;
  font-weight: 500;
  line-height: 1;
  letter-spacing: -0.36px;
  text-transform: uppercase;
  color: var(--tp-common-black);
  position: relative;
  margin-left: 20px;
}

.tp-blog-list-link-wrap {
  margin-top: auto;
}

.tp-blog-list-link::before {
  height: 8px;
  width: 8px;
  content: "";
  border-radius: 50%;
  display: inline-block;
  border: 1px solid var(--tp-common-black);
  position: absolute;
  top: 8px;
  left: -20px;
}

.tp-blog-list-link::after {
  position: absolute;
  bottom: 0;
  right: 0;
  height: 1px;
  width: 0;
  display: inline-block;
  content: "";
  background-color: var(--tp-common-black);
  transition: 0.4s;
}

.tp-blog-list-link:hover {
  color: var(--tp-common-black);
}

.tp-blog-list-link:hover::after {
  right: auto;
  left: 0;
  width: 100%;
}

.tp-blog-list-item {
  padding-bottom: 40px;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(25, 25, 26, 0.12);
}

.tp-blog-list-item:last-child {
  margin-bottom: 0;
}

.tp-blog-list-wrap {
  padding: 50px 155px;
  background-color: #FFFFFF;
  margin-top: -355px;
  position: relative;
  z-index: 22;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .tp-blog-list-wrap {
    padding: 50px 80px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
  .tp-blog-list-wrap {
    padding: 50px 55px;
  }
}

@media (max-width: 767px) {
  .tp-blog-list-wrap {
    padding: 50px 20px;
  }
}

.tp-flex-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.tp-flex-column {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.blog-details-without-sidebar .blog-details-thumb {
  height: 740px;
  overflow: hidden;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .blog-details-without-sidebar .blog-details-thumb {
    height: 550px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-details-without-sidebar .blog-details-thumb {
    height: 400px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-details-without-sidebar .blog-details-thumb {
    height: 300px;
  }
}

@media (max-width: 767px) {
  .blog-details-without-sidebar .blog-details-thumb {
    height: 150px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .blog-details-without-sidebar .blog-details-thumb {
    height: 200px;
  }
}

.blog-details-without-sidebar .blog-details-thumb img {
  margin-top: -300px;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px),
only screen and (min-width: 992px) and (max-width: 1199px),
only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-details-without-sidebar .blog-details-thumb img {
    margin-top: -100px;
  }
}

@media (max-width: 767px) {
  .blog-details-without-sidebar .blog-details-thumb img {
    margin-top: 0px;
  }
}


/* banner css */
.static_banner {
  position: relative;

}

.gallery_slider,
.gallery_slider * {
  height: 100%;
}

.static_banner_box {
  padding: 0px 15px 67px 15px;
  z-index: 99 !important;
}

.static_banner_text h2 {
  font-family: Satoshi;
  font-weight: 900;
  font-size: 34px;
  line-height: 46px;
  color: #fff;
  padding-bottom: 14px;
}

.static_banner_text p {
  font-family: Satoshi;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #fff;
  padding-bottom: 20px;
}

.static_banner_text h3 {
  font-family: Satoshi;
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  padding-bottom: 20px;
  color: #E5E5E5;
}

.slider_dots .slick-dots {
  display: flex !important;
  margin: 0px -5px;
  overflow-x: auto;
  white-space: nowrap;
}

.slider_dots .slick-dots::-webkit-scrollbar {
  display: none;
}

.slider_dots .slick-dots li {
  padding: 0 5px;
  flex: 0 0 20%;
  max-width: 20%;
  cursor: pointer;
}

.slider_dots .slick-dots li img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 1px solid #00000033;
  border-radius: 3px;
  cursor: pointer;
  transition: border 0.3s;
}

.slider_dots .slick-dots li.slick-active img {
  border-color: #EBF9FF;
  /* active border color */
}

.static_banner,
.gallery_slider img {
  min-height: 100vh;
  height: 100vh;
}

.static_banner {
  position: relative;
}

.static_banner:before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9;
}

@media (max-width: 1140px) {

  /* .static_banner,
      .gallery_slider img {
         min-height: calc(100vh - 70px);
      }  */
  .static_banner_box {
    padding: 0px 15px 30px 15px;
  }

  .gallery_slider,
  .gallery_slider>div {
    height: 100%;
  }

  .static_banner_text h2,
  .static_banner_text p,
  .static_banner_text h3 {
    max-width: 768px;
  }
}

@media (max-width: 1024px) {

  .slider_dots .slick-dots li {

    flex: 0 0 25%;
    max-width: 25%;
  }
}

@media (max-width: 768px) {
  .slider_dots .slick-dots li {

    flex: 0 0 33.333%;
    max-width: 33.333%;
  }
}

@media (max-width: 540px) {
  .slider_dots .slick-dots li {

    flex: 0 0 50%;
    max-width: 50%;
  }

  .static_banner_text h2 {
    font-size: 28px;
    line-height: 38px;
    padding-bottom: 10px;
  }

  .static_banner_text p {
    line-height: 24px;
    padding-bottom: 14px;
  }
}



/*  */

.service_service_sec1 {
  padding: 80px 15px 0px 15px;
  margin-bottom: 72px;
  overflow: hidden;
}

.service_service_sec1_box {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 90px;
  align-items: center;
}


.service_sec1_data h2 {
  font-family: Satoshi;
  font-weight: 900;
  font-size: 34px;
  line-height: 44px;
  letter-spacing: unset;
  color: #000;
  margin: 0;
  padding-bottom: 8px;

}

.service_sec1_data h2 span {
  color: #213E96;

}

.service_sec1_data>p {
  font-family: Satoshi;
  font-weight: 400;
  font-size: 16px;
  margin: 0;
  line-height: 25px;


  letter-spacing: 0px;
  color: #000;
  padding-bottom: 25px;
}

.service_sec1_data ul {
  display: grid;
  gap: 12px;
}

.service_sec1_data ul li {
  display: grid;
  grid-template-columns: 64px 1fr;
  gap: 24px;
  align-items: center;
  padding: 10px 0px;
  border-radius: 10px;
  transition: 0.3s ease-in-out;
}

.service_sec1_data ul li:hover {
  box-shadow: 0px 16px 24px 0px #6061701F;

  background-attachment: fixed;
  background-color: #fff;
  transition: 0.3s ease-in-out;

}

.service_sec1_figcaption {
  display: grid;
  gap: 4px;
}

.service_sec1_figcaption h3 {
  font-family: Satoshi;
  font-weight: 900;
  color: #000;
  font-size: 20px;
  line-height: 32px;
  letter-spacing: 0.15px;
}

.service_sec1_figcaption p {
  font-family: Satoshi;
  font-weight: 400;

  font-size: 16px;
  margin: 0px;
  line-height: 23px;
  letter-spacing: unset;
  color: #000;
  padding: 0px;
}

.service_sec1_data ul li img {
  width: 100%;
}

.service_sec1_ab1 {
  position: absolute;
  right: -65px;
  top: -92px;
  z-index: 9;
}

.service_sec1_ab1 img {
  max-width: 215px;
}

.service_sec1_ab2 {
  position: absolute;
  left: -60px;
  bottom: -60px;
  z-index: 9;
}

.service_sec1_ab2 img {
  max-width: 127px;
}

.service_sec1_data_btn {
  padding-top: 54px;
}

.service_sec1_data_btn a {
  border: 1px solid #213E96;
  background-color: #213E96;
  width: 212px;
  height: 56px;
  font-family: Satoshi;
  font-weight: 700;

  font-size: 16px;


  color: #fff !important;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.service_sec1_data_btn a i {
  width: 24px;
  height: 24px;
  background: #fff !important;
  color: #213E96;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(-45deg);
  font-size: 14px;
}

@media(max-width:1250px) {
  .service_sec1_ab2 {
    left: -18px;
    bottom: -60px;
  }
}

@media(max-width:1024px) {
  .service_service_sec1 {
    margin-bottom: 40px;
    padding: 40px 15px 0px 15px;
  }

  .service_sec1_data h2 {

    font-size: 28px;
    line-height: 40px;

  }

  .service_sec1_data_btn {
    padding-top: 30px;
  }

  .service_service_sec1_box {
    gap: 80px;
  }

  .service_sec1_ab1 img {
    max-width: 155px;
  }

  .service_sec1_ab1 {

    right: -40px;
    top: -70px;
  }


}

@media(max-width:768px) {
  .service_sec1_figure_box {
    order: 1 !important;
  }

  .service_sec1_content {
    order: 0 !important;
  }

  .service_service_sec1_box {
    grid-template-columns: 1fr;
    gap: 40px;

  }

  .service_sec1_figure_box {

    max-width: 540px;
    margin: 0 auto;
  }

  .service_sec1_ab1 {

    right: -20px;
    top: -20px;
  }

  .service_sec1_ab2 {

    left: -20px;
    bottom: -20px;
  }
}

@media (max-width: 576px) {

  .service_sec1_data h2,
  .service_sec1_data>p {
    text-align: center;
  }
}

@media (max-width: 540px) {

  .service_sec1_data h2 {
    font-size: 24px;
    line-height: 32px;
  }

  .service_sec1_figcaption h3 {
    line-height: 25px;
    font-size: 18px;

  }

  .service_sec1_data ul {
    gap: 6px;
  }

  .service_sec1_data>p {
    padding-bottom: 20px;
  }

}

.static_banner2 {
  padding: 60px 15px;
  margin-bottom: 72px;
  background: linear-gradient(180deg, #8ECBDB 0%, #42A8C3 100%);
}

.static_banner2_inner {
  background-color: #d6ecf2;
  border-radius: 24px;
  display: grid;
  grid-template-columns: 65% 35%;
  gap: 80px;
  overflow: hidden;

}

.banner2_sliders_inner {
  display: flex;
  gap: 20px;
  margin: -200px 0;
  padding-right: 80px;
}

.banner2_sliders_inner .slick-slide {
  padding: 6px 0px;
}

.slick-slide img {
  max-height: 100%;
  max-width: 100%;
  object-fit: cover;
}

.banner2_slider1,
.banner2_slider2 {
  width: 50%;
  flex: 0 0 50%;
  height: 100%;
}

.banner2_text_box {
  padding: 42px 0px 50px 42px;
}

.static_banner2 h2 {
  font-family: Satoshi;
  font-weight: 900;
  font-style: Black;
  font-size: 34px;
  line-height: 42px;
  letter-spacing: unset;
  text-align: left;
  color: #000;
  margin: 0;
  padding-bottom: 24px;
}

.static_banner2 p {
  font-family: Satoshi;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  text-align: left;
  color: #333333;
  margin: 0;
  max-width: 612px;
  margin-bottom: 30px;
}

.static_banner2_btn a {
  border: 1px solid #213E96;
  background-color: #213E96;
  width: 212px;
  height: 56px;
  font-family: Satoshi;
  font-weight: 700;
  font-size: 16px;
  color: #fff !important;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.static_banner2_btn a i {
  width: 24px;
  height: 24px;
  background: #fff !important;
  color: #213E96;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(-45deg);
  font-size: 14px;
}

.banner2_sliders_inner {
  transform: rotate(17.99deg);
}

.slider-down {
  transform: rotate(180deg);
}

.slider-down .slick-slide {
  transform: rotate(180deg);
}

@media(max-width:1024px) {
  .static_banner2 {
    margin-bottom: 40px;
    padding: 40px 15px;

  }

  .banner2_text_box {
    padding: 40px 0px 40px 30px;
  }

  .banner2_sliders_inner {
    margin: -170px 0;
  }

  .static_banner2_inner {
    grid-template-columns: 60% 40%;
  }

  .static_banner2 h2 {
    font-size: 28px;
    line-height: 30px;
  }


}

@media(max-width:900px) {
  .static_banner2_inner {
    grid-template-columns: 60% 40%;
  }

  .banner2_sliders_inner {
    padding-right: 0;
  }
}

@media(max-width:768px) {

  .static_banner2 h2,
  .static_banner2 p {
    text-align: center;
  }

  .static_banner2_inner {
    grid-template-columns: 100%;
    gap: 40px;
  }

  .banner2_sliders_inner {
    margin: 0;
    padding-right: 0px;
  }

  .slider-down {
    transform: unset;
  }

  .slider-down .slick-slide {
    transform: unset;
  }

  .banner2_slider1,
  .banner2_slider2 {
    width: 100%;
    flex: 0 0 100%;
  }

  .banner2_sliders_inner .slick-slide {
    padding: 0px 6px;
  }

  .banner2_sliders_inner {
    flex-direction: column;
    transform: unset;
    gap: 10px;
    padding-bottom: 30px;
  }

  .banner2_text_box {
    padding: 40px 15px 0px 15px;
  }
}

@media (max-width: 576px) {}

@media(max-width:540px) {
  .static_banner2 h2 {
    font-size: 24px;
  }
}

/*  */

.static_page_banner {
  margin-bottom: 60px;
}

.static_page_banner img {
  min-height: 300px;
  object-fit: cover;
}

.static_page_banner .static_page_text {
  bottom: 101px;
}

.static_page_banner small {
  font-family: Satoshi;
  font-weight: 700;
  font-style: Bold;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  text-transform: uppercase;
  color: #fff;
  display: inline-block;
  text-align: center;
}

.static_page_banner h2 {
  font-family: Satoshi;
  font-weight: 900;
  color: #fff;
  font-size: 190px;
  line-height: 228px;
  letter-spacing: -10px;
  text-transform: uppercase;
  text-align: center;
}

@media(max-width:1500px) {
  .static_page_banner .static_page_text {
    bottom: 70px;
  }

  .static_page_banner h2 {
    font-size: 140px;
    letter-spacing: -6px;
    line-height: 170px;
  }



}

@media(max-width:1024px) {
  .static_page_banner {
    margin-bottom: 40px;
  }

  .static_page_banner .static_page_text {
    bottom: 40px;
  }

  .static_page_banner h2 {
    font-size: 100px;
    letter-spacing: -5px;
    line-height: 128px;
  }
}

@media (max-width: 768px) {
  .static_page_banner small {
    font-size: 14px;
  }

  .static_page_banner h2 {
    font-size: 70px;
    letter-spacing: -2px;
    line-height: 100px;
  }
}

@media (max-width: 540px) {
  .static_page_banner small {
    font-size: 13px;
  }

  .static_page_banner h2 {
    font-size: 60px;
    letter-spacing: -3px;
    line-height: 85px;
  }
}

@media (max-width: 400px) {
  .static_page_banner h2 {
    font-size: 45px;
    letter-spacing: -1px;
    line-height: 75px;
  }
}

/*  */
.static_cus_sec1 {
  padding: 0px 15px 0px 15px;
  margin-bottom: 50px;
  overflow: hidden;
}

.static_sec1_small p {
  font-family: Satoshi;
  font-weight: 400;
  font-size: 20px;
  line-height: 30px;
  letter-spacing: 0;
  color: #000;
  max-width: 982px;
  margin-bottom: 57px;
}


.static_cus_sec1_box {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 30px;
  align-items: center;
}


.cus_sec1_data h2 {
  font-family: Satoshi;
  font-weight: 900;
  font-size: 34px;
  line-height: 44px;
  letter-spacing: unset;
  color: #000;
  margin: 0;
  padding-bottom: 8px;
}


.cus_sec1_data h2 span {
  color: #213E96;

}

.cus_sec1_data small {
  font-family: Satoshi;
  font-weight: 700;
  font-style: Bold;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  display: inline-block;
  color: #333333;
  margin-bottom: 6px;
}

.cus_sec1_data>p {
  font-family: Satoshi;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;


  letter-spacing: 0px;
  color: #000;
  padding-bottom: 25px;
}

.cus_sec1_data ul {
  display: grid;
  gap: 24px;
  /* margin: 0px -24px; */
}

.cus_sec1_data ul li {
  display: grid;
  grid-template-columns: 64px 1fr;
  gap: 24px;
  align-items: center;
  padding: 18px 24px;
  border-radius: 10px;
  transition: 0.3s ease-in-out;
}

.cus_sec1_data ul li:hover {
  box-shadow: 0px 16px 24px 0px #6061701F;

  background-attachment: fixed;
  background-color: #fff;
  transition: 0.3s ease-in-out;

}

.cus_sec1_figcaption {
  display: grid;
  gap: 4px;
}

.cus_sec1_figcaption h3 {
  font-family: Satoshi;
  font-weight: 900;
  color: #000;
  font-size: 20px;
  line-height: 32px;
  margin: 0px;
  letter-spacing: 0.15px;
}

.cus_sec1_figcaption p {
  font-family: Satoshi;
  font-weight: 400;
  margin: 0px;
  font-size: 16px;

  line-height: 23px;
  letter-spacing: unset;
  color: #000;
  padding: 0px;
}

.cus_sec1_data ul li img {
  width: 100%;
}

.cus_sec1_ab1 img {
  max-width: 215px;
}


@media(max-width:1024px) {
  .static_cus_sec1 {
    margin-bottom: 40px;
  }

  .cus_sec1_data h2 {

    font-size: 28px;
    line-height: 40px;

  }



  .static_sec1_small p {
    font-size: 18px;
    line-height: 26px;
    max-width: 800px;
    margin-bottom: 30px;
  }

  .cus_sec1_data ul li {
    padding: 18px 18px;
  }



  .cus_sec1_data ul {
    gap: 20px;
  }
}

@media(max-width:768px) {
  .cus_sec1_figure_box {
    order: 1 !important;
  }

  .cus_sec1_content {
    order: 0 !important;
  }

  .static_cus_sec1_box {
    grid-template-columns: 1fr;
    gap: 40px;
  }

}

@media (max-width: 576px) {
  .static_sec1_small p {
    font-size: 16px;
    line-height: 25px;
  }

  .cus_sec1_data small {
    display: flex;
    justify-content: center;
  }


  .cus_sec1_data h2,
  .cus_sec1_data>p {
    text-align: center;
  }
}

@media (max-width: 540px) {

  .cus_sec1_data h2 {
    font-size: 24px;
    line-height: 32px;
  }

  .cus_sec1_figcaption h3 {
    line-height: 25px;
    font-size: 18px;
  }

  .cus_sec1_data ul {
    gap: 10px;
  }

  .cus_sec1_data>p {
    padding-bottom: 20px;
  }

}

/*  */

.static_sec2 {
  margin-bottom: 72px;
  padding: 0 15px;
  overflow: hidden;
}

.static_sec2_content {
  max-width: 1045px;
}

.static_sec2_content h2 {
  font-family: Satoshi;
  font-weight: 900;
  font-size: 34px;
  line-height: 42px;
  letter-spacing: 0;
  text-align: left;
  color: #000;
  margin: 0;
  padding-bottom: 30px;
  max-width: 1027px;
}

.static_sec2_content h2 span {
  color: #213E96;
  font-family: Satoshi;
  font-weight: 900;
}



.sec2_inner_text_box a {
  width: 190px;
  height: 56px;
  border-radius: 20px;
  border: 1px solid #E3EAFF;
  font-family: Satoshi;
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  color: #000;
  background-color: #fff;
}

.sec2_inner_text_box a img {
  max-width: 24px;
}

@media(max-width:1024px) {
  .static_sec2 {
    margin-bottom: 40px;
  }

  .static_sec2_content h2 {
    font-size: 28px;
    line-height: 36px;
  }

  .sec2_inner_text_box a {
    width: 166px;
    height: 46px;
    font-size: 15px;
  }
}

@media(max-width:768px) {
  .sec2_inner_text_box a {
    width: 146px;
    height: 42px !important;
    font-size: 14px;
  }

  .sec2_inner_text_box a img {
    max-width: 20px;
  }

}

@media(max-width:576px) {


  .static_sec2_content h2 {
    text-align: center;
  }
}

@media(max-width:540px) {
  .static_sec2_content h2 {
    font-size: 24px;
    line-height: 32px;
  }



}

/*  */

.static_contacts {
      margin-bottom: 72px;
      padding: 0 15px;
   }

   .static_contact_links {
      gap: 30px;
      justify-content: space-between;
   }

   .contact_link_box h3 {
      font-family: Satoshi;
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0px;
      color: #000;
      margin: 0;
      padding-bottom: 10px;
   }

   .contact_link_box a {
      font-family: Satoshi;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0px;
      color: #000 !important;
      display: inline-block;
   }

   @media(max-width:1130px) {
      .static_contact_links {}
   }

   @media(max-width:1024px) {


      .static_contacts {
         margin-bottom: 40px;
      }
   }

   @media(max-width:540px) {
      .static_contact_links {
         gap: 25px 0;
      }

      .contact_link_box {
         flex: 0 0 50%;
      }
   }
   /*  */
    .contact_cus_sec1 {
      padding: 0px 15px 0px 15px;
      margin-bottom: 72px;
      overflow: hidden;
   }

   .contact_cus_sec1_box {
      display: grid;
      grid-template-columns: 1fr 1.2fr;
      gap: 80px;
      align-items: center;
   }


   .cus_sec1_ab1 {
      position: absolute;
      right: -65px;
      top: -92px;
      z-index: 9;
   }

   .cus_sec1_ab1 img {
      max-width: 215px;
   }

   .cus_sec1_ab2 {
      position: absolute;
      left: -60px;
      bottom: -60px;
      z-index: 9;
   }

   .cus_sec1_ab2 img {
      max-width: 127px;
   }



   @media(max-width:1250px) {
      .cus_sec1_ab2 {
         left: -18px;
         bottom: -60px;
      }
   }

   @media(max-width:1024px) {
      .contact_cus_sec1 {
         margin-bottom: 40px;
         padding: 0px 15px 0px 15px;
      }

      .contact_cus_sec1_box {
         gap: 80px;

      }

      .cus_sec1_ab1 img {
         max-width: 155px;
      }

      .cus_sec1_ab1 {

         right: -40px;
         top: -70px;
      }


   }

   @media(max-width:768px) {
      .cus_sec1_figure_box {
         order: 1 !important;
      }

      .cus_sec1_content {
         order: 0 !important;
      }

      .contact_cus_sec1_box {

         grid-template-columns: 1fr;
         gap: 40px;

      }

      .cus_sec1_figure_box {

         max-width: 540px;
         margin: 0 auto;
      }

      .cus_sec1_ab1 {

         right: -20px;
         top: -20px;
      }

      .cus_sec1_ab2 {

         left: -20px;
         bottom: -20px;
      }
   }
   /*  */
    .contact_sec_form {
      background-color: transparent;

   }

   .contact_sec_form h2 {
      font-family: Satoshi;
      font-weight: 900;
      font-size: 34px;
      line-height: 42px;
      letter-spacing: 0;
      text-align: left;
      color: #000;
      margin: 0;
      padding-bottom: 24px;
   }

   .contact_sec_form p {
      font-family: Satoshi;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0;
      color: #000;
      margin-bottom: 48px;
   }

   .contact_sec_form_content {
      gap: 24px;
      grid-template-columns: 1fr;
   }

   .contact_sec_form_content span {
      font-family: Satoshi;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0;
      display: inline-block;
      margin-bottom: 6px;
   }

   .contact_sec_form_content label {
      font-size: 13px;
   }

   .contact_sec_form_content input {
      border: 1px solid #D0D5DD;
      padding: 12px 16px;
      outline: none;
      font-family: Satoshi;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0;
      color: #000;
      border-radius: 8px;
   }

   .contact_sec_form_content input.message {
      height: 128px;
      padding-bottom: 90px;

   }

   .contact_sec_form_content input::placeholder {
      color: #667085;
   }

   .contact_sec_form button {
      width: 100%;
      height: 56px;
      border-radius: 20px;
      border: 1px solid #213E96;
      font-family: Satoshi;
      font-weight: 700;
      font-size: 16px;
      line-height: 100%;
      letter-spacing: 0;
      color: #fff;
      background-color: #213E96;
   }

   .contact_sec_form button img {
      max-width: 24px;
   }



   @media(max-width:1500px) {}



   @media(max-width:1024px) {


      .cust_contact_sec {
         margin-bottom: 40px;
      }

      .contact_sec_form h2 {
         font-size: 28px;
         line-height: 36px;
         padding-bottom: 15px;
      }

      .contact_sec_form button {
         height: 46px;
         font-size: 15px;
      }

      .contact_sec_form p {
         margin-bottom: 25px;
      }
   }

   @media(max-width:768px) {

      .contact_sec_form button {
         height: 42px !important;
         font-size: 14px;
      }

      .contact_sec_form button img {
         max-width: 20px;
      }

      .contact_sec_form_content input.message {
         height: 100px;
         padding-bottom: 70px;
      }

   }

   @media(max-width:576px) {




      .cust_contact_sec figure {
         order: 2;
      }


      .contact_sec_form h2,
      .contact_sec_form p {
         text-align: center;
      }

      .contact_sec_form_content {
         gap: 16px;
      }
   }

   @media(max-width:540px) {
      .contact_sec_form h2 {
         font-size: 24px;
         line-height: 32px;
      }


      .contact_sec_form_content input {
         font-size: 14px;
         line-height: 20px;
      }

   }
   /*  */
    .cust1_heading_sec {
      padding: 0 15px;
      margin-bottom: 48px;
      margin-top: 72px;
   }

   .cust1_heading_main h2 {
      font-family: Satoshi;
      font-weight: 900;
      font-style: Black;
      font-size: 34px;
      line-height: 100%;
      letter-spacing: unset;
      text-align: center;
      color: #000;
      margin: 0;
   }

   .cust1_heading_main h2 span {

      color: #213E96 !important;
   }

   @media(max-width:1500px) {}



   @media(max-width:1024px) {
      .cust1_heading_sec {
         margin-bottom: 30px;
         margin-top: 60px;
      }

      .cust1_heading_main h2 {
         font-size: 28px;
         line-height: 32px;
      }

   }

   @media(max-width:768px) {}

   @media(max-width:540px) {
      .cust1_heading_main h2 {
         font-size: 24px;

      }
   }

   /*  */
     .brand_section {
      margin-bottom: 72px;
      padding: 0px 15px;
    float:left;
    }




   .brand_list ul {
      margin: 0px -12px;
      padding: 0px;
   }

   .brand_list ul li {
      list-style: none;
      width: 25%;
      padding: 0px 12px 0px 12px;
      float: left;
      margin: 0px !important;
   }

   .brand_slider_arrow_box {
      display: flex;
      justify-content: space-between;
      height: 100%;
      align-items: center;
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
   }

   .brand_slider_arrow_box a.brand_pre_arrow.slick-arrow {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99;
   }

   .brand_slider_arrow_box a.brand_pre_arrow.slick-arrow i {
      color: #000;
   }

   .brand_figure_box figure {
      max-width: 164px;
   }

   @media (max-width: 1024px) {
      .brand_section {
         margin-bottom: 50px;

      }

      .brand_list ul {
         margin: 0px -10px;
      }

      .brand_list ul li {
         padding: 0px 10px 0px 10px;
      }


   }

   /*  */
    .cus_heading2 {
      padding: 0 15px;
      margin-bottom: 48px;
   }

   .cust_heading2_box {
      max-width: 868px;
      margin: 0 auto;
   }

   .cust_heading2_box h2 {
      font-family: Satoshi;
      font-weight: 900;
      font-style: Black;
      font-size: 34px;
      line-height: 48px;
      letter-spacing: unset;
      text-align: center;
      color: #000;
      margin: 0;
      padding-bottom: 20px;
   }

   .cust_heading2_box p {
      font-family: Satoshi;
      font-weight: 400;
      font-size: 16px;
      line-height: 26px;
      text-align: center;

   }

   .cust_heading2_box h2 span {

      color: #213E96 !important;
   }



   @media(max-width:1024px) {
      .cus_heading2 {
         margin-bottom: 30px;

      }

      .cust_heading2_box h2 {
         font-size: 28px;
         line-height: 32px;
      }

   }

   @media(max-width:768px) {}

   @media(max-width:540px) {
      .cust_heading2_box h2 {
         font-size: 24px;
      }
   }

   /*  */
    .cat_section {

      padding: 0px 15px;
   }

   .cus_sec_btn {
      margin-bottom: 70px;
   }



   .cat_list ul {
      margin: 0px -12px;
      padding: 0px;
   }

   .cat_list ul li {
      list-style: none;
      width: 25%;
      padding: 0px 12px 0px 12px;
      float: left;
      margin: 0px !important;
   }

   .cat_slider_arrow_box {
      display: flex;
      justify-content: space-between;
      height: 100%;
      align-items: center;
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
   }

   .cat_slider_arrow_box a.brand_pre_arrow.slick-arrow {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99;
   }

   .cat_slider_arrow_box a.brand_pre_arrow.slick-arrow i {
      color: #000;
   }


   .cat_ab1 {
      position: absolute;
      top: -29px;
      left: 0px;
      width: 100%;
      z-index: 9;
      background-image: url('https://static.tossdown.com/images/1f95da7b-8b4f-425a-9b10-4b0920df2ad6.webp');
      height: 44px;
      background-position: top;
      /* background-size: cover; */
   }


   .cat_ab2 {
      position: absolute;
      bottom: -10px;
      left: 0px;
      width: 100%;
      z-index: 9;
      background-image: url('https://static.tossdown.com/images/c67485f5-77d6-4dbc-aea9-dbdb1729f59c.webp');
      height: 44px;
      background-position: top;
      /* background-size: cover; */
   }

   .cat_figure_box a span {
      position: absolute;
      bottom: 40px;
      font-family: Satoshi;
      font-weight: 400;
      font-size: 18px;
      line-height: 100%;
      text-transform: uppercase;
      padding: 0px 20px;
      color: #fff;
      letter-spacing: unset !important;
      transition: transform 0.3s ease;
   }

   .cat_figure_box:hover a span {
      transform: translateY(-10px);
      /* move upward */
   }


   .cus_sec_btn {
      padding-top: 30px;
   }

   .cus_sec_btn a {
      border: 1px solid #E3EAFF;
      background-color: #fff;
      width: 175px;
      height: 56px;
      font-family: Satoshi;
      font-weight: 700;

      font-size: 16px;


      color: #213E96 !important;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
   }

   .cus_sec_btn a i {
      width: 24px;
      height: 24px;
      background: #213E96 !important;
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotate(-45deg);
      font-size: 14px;
   }

   @media (max-width: 1024px) {
      .cat_list ul {
         margin: 0px -10px;
      }

      .cus_sec_btn {
         margin-bottom: 40px;
      }


      .cat_list ul li {
         padding: 0px 10px 0px 10px;
      }


   }

   @media (max-width: 768px) {

      .cat_ab1,
      .cat_ab2 {

         height: 28px;

      }

      .cat_figure_box a span {

         bottom: 20px;

         font-size: 16px;

         padding: 0px 15px;

      }
   }
   /*  */

    .hom_cus2_heading {

      margin-bottom: 30px;

   }

   .hom_cus2_heading h2 {
      font-family: Satoshi;
      font-weight: 900;
      font-style: Black;
      font-size: 34px;
      line-height: 48px;
      letter-spacing: unset;
      text-align: center;
      color: #000;
      margin: 0;
   }

   .hom_cus2_heading h2 span {

      color: #213E96 !important;
   }

   @media(max-width:1500px) {}



   @media(max-width:1024px) {

      .hom_cus2_heading h2 {
         font-size: 28px;
         line-height: 32px;
      }

   }

   @media(max-width:540px) {
      .hom_cus2_heading h2 {
         font-size: 24px;

      }
   }

   .home_cus2_sec {
      padding: 0px 15px;
      overflow: hidden;
   }




   .home_cus2_box {

      margin: 0px -14px;
   }

   .home_cus2_box li {
      list-style: none;
      flex: 0 0 50%;
      padding: 0px 14px;
      max-width: 50%;
      gap: 20px;
   }

   .home_cus2_data h3 {
      font-family: Satoshi;
      font-weight: 700;
      font-size: 24px;
      line-height: 34px;
      letter-spacing: unset;
      color: #000;
      margin: 0;
      padding-bottom: 20px;
   }

   .home_cus2_btn a {
      font-family: Satoshi;
      font-weight: 700;
      font-size: 16px;
      color: #213E96 !important;
      border-radius: 20px;
      display: flex;
      align-items: center;
      gap: 12px;
   }

   .home_cus2_btn a i {
      width: 24px;
      height: 24px;
      background: #213E96 !important;
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotate(-45deg);
      font-size: 14px;
   }

   @media (max-width: 1200px) {

      .home_cus2_box {
         margin: 0px -10px;
      }

      .home_cus2_box li {

         padding: 0px 10px;

      }
   }

   @media (max-width: 1024px) {


      .home_cus2_data h3 {

         font-size: 20px;
         line-height: 26px;
      }


   }

   @media (max-width: 768px) {
      .home_cus2_box li {

         flex: 0 0 100%;

         max-width: 100%;
      }

      .home_cus2_box {

         flex-wrap: wrap;
         gap: 30px;
      }
   }

   @media (max-width:576px) {
      .home_cus2_data h3 {
         text-align: center;
      }
   }

   @media (max-width:540px) {
      .home_cus2_data h3 {
         font-size: 18px;
         line-height: 26px;
      }

      .home_cus2_box li {

         padding: 0px 14px;

         gap: 15px;
      }
   }