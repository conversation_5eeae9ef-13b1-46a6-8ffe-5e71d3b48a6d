@use '../utils' as *;

/*----------------------------------------*/
/*  2.12 Accordion
/*----------------------------------------*/

.tp-service-2{
    &-accordion-box{
        & .accordion-items{
            border-bottom: 1px solid rgba(25, 25, 26, 0.10);
            position: relative;
        }
        & .accordion-header{
            & .accordion-buttons{
                padding: 20px 0;
                padding-left: 30px;
                padding-right: 50px;
                font-size: 26px;
                font-weight: 400;
                line-height: 1;
                color: var(--tp-common-black);
                width: 100%;
                text-align: left;
                &:not(.collapsed) .accordion-icon::before {
                    transform: translate(-50%, -50%) rotate(90deg);
                }
                & .accordion-icon{
                    position: absolute;
                    top: 40px;
                    right: 0;
                    &::before {
                        position: absolute;
                        content: "";
                        width: 2px;
                        height: 20px;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #525258;
                        border-radius: 2px;
                        transition: 0.4s;
                    }
                    &::after {
                        position: absolute;
                        content: "";
                        width: 20px;
                        height: 2px;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        background-color: #525258;
                        border-radius: 2px;
                    }
                }
            }
            & span{
                margin-right: 20px;
            }
        }
        & .accordion-body{
            padding: 0px 10px 40px 85px;
            @media #{$xs}{
                padding: 0px 10px 40px 50px;
            }
            & p{
                margin-bottom: 40px;
            }
        }
    }
}
.fq-faq-wrapper{
    & .tp-service-2-accordion-box .accordion-header .accordion-buttons {
        padding: 35px 0;
        padding-left: 0px;
        padding-right: 50px;
        font-size: 30px;
        font-weight: 600;
        color: var(--tp-common-black);
        @media #{$xs}{
            font-size: 20px;
        }
    }
    & .tp-service-2-accordion-box .accordion-body {
        padding: 0;
        padding-right: 100px;
        @media #{$xs}{
            padding: 0;
        }
    }
}
.tp-service-2-accordion-box{
    @media #{$md,$xs}{
        margin-bottom: 60px;
    }
}
.fq-faq{
    &-bdr{
        border-top: 1px solid rgba(25, 25, 26, 0.10);  ;
    }
    &-sidebar{
        padding-left: 70px;
        @media #{$lg}{
            padding-left: 20px;
        }
        @media #{$md,$xs}{
            padding-left: 0px;
        }
        &-title{
            font-size: 60px;
            font-weight: 600;
            line-height: 1;
            margin-bottom: 15px;
            color: var(--tp-common-black-2);
        }
        &-content{
            & p{
                color: #5D5D63;
                font-weight: 400;
                line-height: 26px;
                margin-bottom: 45px;
                @media #{$md,$xs}{
                    & br{
                        display: none;
                    }
                }
            }
        }
        &-input{
            & input{
                border: none;
                padding-left: 9px;
                background-color: transparent;
                border-bottom: 2px solid #D9D9D9;
                color: #5D5D63;
                font-size: 18px;
                font-weight: 400;
                line-height: 1;
                @include placeholder{
                    color: #5D5D63;
                    font-size: 18px;
                    font-weight: 400;
                    line-height: 1;
                }
            }
        }
        &-thumb{
            margin-bottom: 40px;
        }
        &-search{
            position: absolute;
            top: 15px;
            right: 0;
        }
    }
}
