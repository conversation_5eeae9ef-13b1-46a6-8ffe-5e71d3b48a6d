<?php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserAccountMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $type;
    public $password;
    public $token;

    /**
     * Create a new message instance.
     */
    public function __construct($userCreated,$type,$password,$resetToken)
    {
        $this->user = $userCreated;
        $this->type = $type;
        $this->password = $password;
        $this->token = $resetToken;
    }
 
    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject("Welcome to Jim's Bathrooms Online")
            ->view('emails.userAccountMail')
            ->with([
                'userName' => $this->user->name,
                'userType' => $this->type,
                'email' => $this->user->email,
                'password' => $this->password, // Send the password (Optional: you might send a password reset link instead)
                'resetUrl' => route('password.reset', ['token' => $this->token]),
            ]);
    }
}
