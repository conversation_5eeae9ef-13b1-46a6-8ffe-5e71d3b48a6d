<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class States extends Model
{
    use HasFactory;

             // 🔽 Add this line to explicitly set the table name
    protected $table = 'states';


    protected $fillable = [
        'id',
        'country_id',
        'name',
        'created_at',
        'updated_at',
    ];

    public function country() {
        return $this->belongsTo(Countries::class, 'country_id'); 
    }
 
}