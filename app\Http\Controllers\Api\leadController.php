<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Leads;
use App\Models\User;
use App\Models\Franchisees;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class leadController extends Controller
{




    public function storeLead(Request $request)
    {
        $user = Auth::user();

        // Validate the request
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',

            'address' => 'required|string|max:255',
            'phone' => 'required|numeric',

            'service' => 'required|string|max:255',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }


        // Create the association record
        $lead = Leads::create([
            'franchisee_id' => $user->franchisee->id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'address' => $request->address,
            'phone' => $request->phone,
            'email' => $request->email,
            'service' => $request->service,
            'notes' => $request->notes,

            'status' => 'follow-up required',

        ]);


        return response()->json(['message' => 'Lead created successfully!', "status" => "success"], 200);
    }

    public function updateLead(Request $request, $id)
    {
        // Find the lead by ID
        $lead = Leads::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',

            'address' => 'required|string|max:255',
            'phone' => 'required|numeric',

            'service' => 'required|string|max:255',
            'status' => 'required|string|max:255',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Update lead data
        $lead->first_name = $request->first_name;
        $lead->last_name = $request->last_name;
        $lead->address = $request->address;
        $lead->phone = $request->phone;
        $lead->email = $request->email;
        $lead->service = $request->service;
        $lead->status = $request->status;
        $lead->notes = $request->notes;

        $lead->save();
        return response()->json(['message' => 'Lead updated successfully!', "status" => "success"], 200);
    }


    public function sendBulkMail(Request $request)
    {
        $request->validate([
            'lead_ids' => 'required|string',
            'email_subject' => 'required',
            'message' => 'required',
        ]);

        $leadIds = explode(',', $request->lead_ids);
        $leads = Leads::whereIn('id', $leadIds)->get();



        foreach ($leads as $lead) {
            try {
                $fullName = $lead->first_name . ' ' . $lead->last_name;
                sendSingleLeadMail($fullName, $lead->email, $request->email_subject, $request->message,$request->sender_name,$request->sender_email);
            } catch (\Exception $e) {
                Log::error("Bulk email failed for lead {$lead->id}: " . $e->getMessage());
            }
        }

        return response()->json([
            'message' => 'Bulk email sent successfully!',
            'status' => 'success'
        ], 200);
    }


    public function deleteLead($id)
    {
        // Find the lead by ID
        $lead = Leads::findOrFail($id);
        $lead->delete();

        return response()->json(['message' => 'Lead deleted successfully!', "status" => "success"], 200);
    }
}
