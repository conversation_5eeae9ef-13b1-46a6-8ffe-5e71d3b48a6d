@use '../../utils' as *;

/*----------------------------------------*/
/*  7.22 team css start
/*----------------------------------------*/

.tp-team {
    &-item {
        position: relative;
        text-align: center;
        & img{
            width: 100%;
            transition: .7s;
            transform: scale(1);
            object-fit: cover;
        }
        &:hover{
            & img{
                transform: scale(1.3);
            }
        }
        &::after {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0%;
            content: '';
            background: linear-gradient(178.92deg, rgba(29, 29, 29, 0) 27.4%, #1D1D1D 108.05%);
            opacity: 0;
            visibility: hidden;
            transition: all .3s;
        }

        &:hover {
            &::after {
                opacity: 1;
                visibility: visible;
                height: 100%;
            }

            & .tp-team-content {
                & span {
                    transform: translateY(0);
                    visibility: visible;
                    opacity: 1;
                    transition-delay: 0.2s;
                }
            }

            & .tp-team-title-sm {
                transform: translateY(0);
                visibility: visible;
                opacity: 1;
                transition-delay: 0.2s;
            }
        }
    }

    &-content {
        position: absolute;
        z-index: 2;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 15px;
        padding-bottom: 20px;

        & span {
            font-weight: 500;
            font-size: 14px;
            line-height: 14px;
            text-transform: uppercase;
            color: var(--tp-common-white);
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease-out 0s;
            transition-delay: 0.2s;
            display: inline-block;
        }
    }

    &-title-sm {
        font-weight: 400;
        font-size: 50px;
        line-height: 1;
        text-transform: uppercase;
        color: var(--tp-common-white);
        font-family: var(--tp-ff-gallery);
        opacity: 0;
        visibility: hidden;
        transform: translateY(30px);
        transition: all 0.5s ease-out 0s;
        transition-delay: 0.2s;

        @media #{$xl} {
            font-size: 40px;
        }

        @media #{$lg,$md,$xs} {
            font-size: 30px;
        }
    }

    &-slider-active {
        margin: 0px -260px;
        @media #{$xl,$md,$xs} {
            margin: 0;
        }
    }
}
.tp-team-4 {
    &-item {
        border-radius: 10px;
        transition: .3s;
        overflow: hidden;
        border: 1px solid rgba(25, 25, 26, 0.10);
        & img{
            width: 100%;
            transition: .7s;
            transform: scale(1);
            object-fit: cover;
        }
        &:hover{
            & img{
                transform: scale(1.3);
            }
        }
    }
    &-thumb {
        overflow: hidden;
        position: relative;
        & img {
            transition: .3s;
            border-radius: 4px;
        }
    }

    &-social {
        & a {
            height: 36px;
            width: 36px;
            line-height: 36px;
            border-radius: 50%;
            display: inline-block;
            text-align: center;
            color: var(--tp-common-black);
            border: 1px solid rgba(25, 25, 26, 0.10);
            margin: 0px 4px;
            transition: .3s;

            &:hover {
                color: var(--tp-common-white);
                border-color: var(--tp-common-black);
                background-color: var(--tp-common-black);
            }
        }
    }

    &-content {
        padding: 30px 70px 20px 70px;

        @media #{$md} {
            padding: 30px 50px 20px 50px;
        }

        @media #{$xs} {
            padding: 30px 40px 20px 40px;
        }

        & span {
            font-size: 13px;
            font-weight: 400;
            line-height: 1;
            color: #5D5D63;
            letter-spacing: 0.26px;
            text-transform: uppercase;
            margin-bottom: 8px;
            display: inline-block;
        }
    }

    &-title-sm {
        font-size: 30px;
        font-weight: 400;
        line-height: 1;
        margin-bottom: 20px;
        padding-bottom: 15px;
        color: var(--tp-common-black);
        font-family: var(--tp-ff-marcellus);
        border-bottom: 1px solid rgba(25, 25, 26, 0.10);
    }
}
.tm-hero{
    &-ptb{
        padding-top: 200px;
        padding-bottom: 80px;
        @media #{$md}{
            padding-top: 150px;
        }
        @media #{$xs}{
            padding-top: 140px;
        }
    }
    &-subtitle{
        color: #5D5D63;
        font-size: 18px;
        font-weight: 500;
        line-height: 1;
        &::after{
            content: "";
            height: 1px;
            width: 40px;
            background-color: #5D5D63;
            display: inline-block;
            transform: translateY(-3px);
            margin-left: 14px;
        }
    }
    &-title{
        font-size: 120px;
        font-weight: 500;
        line-height: 1;
        letter-spacing: -7.2px;
        color: var(--tp-common-black-2);
        padding-bottom: 15px;
        @media #{$md}{
            font-size: 110px;
        }
        @media #{$xs}{
            font-size: 65px;
        }
        &.fs-220{
            font-size: 220px;
            letter-spacing: -8.8px;
            @media #{$xl,$lg}{
                font-size: 180px;
            }
            @media #{$md}{
                font-size: 140px;
            }
            @media #{$xs}{
                font-size: 105px;
            }
        }
    }
    &-title-big{
        font-size: 200px;
        font-weight: 500;
        line-height: .9;
        letter-spacing: -12px; 
        color: var(--tp-common-black-2);
        @media #{$lg}{
            font-size: 180px;
        }
        @media #{$md}{
            font-size: 140px;
        }
        @media #{$xs}{
            font-size: 100px;
        }
    }
    &-text{
        padding-left: 200px;
        @media #{$xs}{
            padding-left: 0;
        }
        & p{
            font-size: 18px;
            font-weight: 400;
            line-height: 26px;
            color: #5D5D63;
            max-width: 460px;
            @media #{$xs}{
                & br{
                    display: none;
                }
            }
        }
    }
}
.tm-details{
    &-content-wrap{
        padding-top: 300px;
        padding-bottom: 130px;
        @media #{$xl,$lg,$md,$xs}{
            padding-top: 100px;
            padding-bottom: 100px;
        }
    }
    &-title{
        color: #000;
        font-size: 60px;
        font-weight: 600;
        line-height: 1;
        letter-spacing: -1.2px;
        @media #{$md}{
            font-size: 55px;
        }
        @media #{$xs}{
            font-size: 40px;
        }
    }
    &-social-title{
        font-size: 20px;
        font-weight: 600;
        line-height: 1;
        color: var(--tp-common-black-2);
        display: block;
        margin-bottom: 15px;
    }
    &-text{
        & p{
            color: #5D5D63;
            font-size: 18px;
            font-weight: 400;
            line-height: 26px;
            margin-bottom: 50px;
            @media #{$md,$xs}{
                font-size: 16px;
            }
        }
    }
    &-portfolio{
        & a{
            font-size: 14px;
            font-weight: 500;
            height: 35px;
            line-height: 35px;
            border-radius: 40px;
            padding: 0px 27px;
            display: inline-block;
            letter-spacing: -0.14px;
            text-transform: uppercase;
            margin-right: 10px;
            color: var(--tp-common-black-2);
            border: 1.5px solid rgba(25, 25, 26, 0.20);
            transition: .3s;
            @media #{$md}{
                padding: 0px 25px;
            }
            @media #{$xs}{
                margin-bottom: 5px;
            }
            &:hover{
                color: var(--tp-common-white);
                border-color: var(--tp-common-black-2);
                background-color: var(--tp-common-black-2);
            }
        }
    }
    &-social{
        & a{
            height: 45px;
            width: 45px;
            line-height: 45px;
            border-radius: 50%;
            text-align: center;
            display: inline-block;
            color: var(--tp-common-black-2);
            border: 1.5px solid rgba(25, 25, 26, 0.20);
            transition: .3s;
            margin-right: 8px;
            &:hover{
                border-color: var(--tp-common-black-2);
            }
        }
    }
    &-thumb{
        & img{
            max-width: inherit;
            margin-left: -100px;
            margin-bottom: -20px;
            position: relative;
            z-index: 2;
            @media #{$md}{
                max-width: 100%;
                margin-left: 0;
            }
            @media #{$xs}{
                display: none;
            }
        }
    }
    &-shape{
        &-1{
            position: absolute;
            top: 0;
            left: 0;
        }
        &-2{
            position: absolute;
            bottom: 0;
            right: 0;
        }
    }
}
.tm-details-wrapper {
	height: 100vh;
    overflow: hidden;
}

.tm-testimonial-height{
    height: 100vh;
    @media #{$lg,$md,$xs}{
        height: 100%;
    }
}