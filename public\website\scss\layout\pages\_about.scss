@use '../../utils' as *;

/*----------------------------------------*/
/*  7.1 about css start
/*----------------------------------------*/

.tp-about-2{
    &-section-title{
        color: var(--tp-common-black);
        font-size: 70px;
        font-weight: 500;
        line-height: 1.1;
        letter-spacing: -2.8px;
        @media #{$xs}{
            font-size: 48px;
        }
    }
    &-thumb{
        &-box{
            margin-right: 35px;
            @media #{$xs}{
                margin-right: 0;
            }
        }
        &-main{
            & img{
                transform-origin: top left;
                border-radius: 12px;
                @media #{$xs}{
                    width: 100%;
                    transform: rotate(0);
                    margin-bottom: 20px;
                }
            }
        }
        &-inner{
            position: absolute;
            bottom: -40px;
            left: 153px;
            transform-origin: left bottom;
            @media #{$xs}{
                transform: rotate(0);
                position: static;
                margin-bottom: 20px;
            }
            & img{
                border-radius: 12px;
                width: 100%;
                margin-bottom: 10px;
            }
        }
        &-text{
            color: #37373D;
            font-size: 15px;
            font-weight: 400;
            line-height: 1; 
            text-transform: uppercase;
            white-space: nowrap;
            display: inherit;
        }
    }
    &-content{
        padding-left: 40px;
        padding-right: 55px;
        @media #{$xl}{
            padding-left: 0;
            padding-right: 0;
        }
        @media #{$lg,$md,$xs}{
            padding-left: 0;
            padding-right: 0;
            margin-bottom: 80px;
        }
        & span{
            color: var(--tp-common-black);
            font-size: 18px;;
            font-weight: 600;
            line-height: 1.3;
            text-transform: uppercase;
            margin-bottom: 30px;
            display: inline-block;
        }
        & p{
           color: #3E3E44;
        }
    }
    &-right-thumb{
        overflow: hidden;
        position: relative;
        width: 200px;
        height: 230px;
        @media #{$lg,$md}{
            width: 100%;
        }
    }
    &-area{
        @media #{$xs}{
            padding-top: 60px;
            padding-bottom: 100px;
        }
    }
}
.tp-about-3{
    &-content{
        padding-right: 230px;
        @media #{$lg,$md,$xs}{
            padding-right: 0;
        }
    }
    &-shape{
        margin-right: 160px;
        @media #{$md}{
            margin-right: 0;
        }
        @media #{$xs}{
            margin-right: 0;
            margin-bottom: 20px;
        }
        & img{
            animation: rotate2 5s linear infinite;
        }
    }
}
.tp-about-4{
    &-title{
        font-size: 50px;
        font-weight: 400;
        line-height: 1.1;
        letter-spacing: -0.5px;
        color: #F5F7F5;
        font-family: var(--tp-ff-marcellus);
        padding-right: 45px;
        margin-bottom: 60px;
        @media #{$md}{
            padding-right: 0;
            font-size: 35px;
        }
        @media #{$xs}{
            padding-right: 0;
            font-size: 33px;
        }
    }
    &-content{
        @media #{$md,$xs}{
            margin-bottom: 40px;
        }
        &.item-1{
            & p{
                padding-right: 50px;
                @media #{$xl,$md,$xs}{
                    padding-right: 0;
                }
            }
        }
        &.item-2{
            & p{
                padding-right: 30px;
            }
        }
        & p{
            font-size: 18px;
            font-weight: 400;
            line-height: 28px;
            color: rgba(255, 255, 255, 0.70);
        }
        & a{
            font-size: 16px;
            font-weight: 600;
            letter-spacing: -0.16px;
            text-transform: uppercase; 
            color: var(--tp-common-white);
            background-image: linear-gradient(#fff, #fff), linear-gradient(#fff, #fff);
            display: inline;
            background-size: 0% 1px, 0 1px;
            background-position: 100% 100%, 0 100%;
            background-repeat: no-repeat;
            transition: background-size 0.3s linear;
            &:hover{
                background-size: 0% 1px, 100% 1px;
            }
        }
    }
    &-content-wrap{
        margin-right: -90px;
        @media #{$xl,$lg,$md,$xs}{
            margin-right: 0;
        }
    }
    &-shape{
        &-1{
            padding-left: 25px;
            @media #{$xs}{
                padding-left: 0;
                margin-bottom: 30px;
            }
            & img{
                animation: animationglob 5s cubic-bezier(1, 0.99, 0.03, 0.01) infinite;
            }
        }
        &-2{
            position: absolute;
            top: 120px;
            right: 250px;
            @media #{$xl,$md}{
                right: 70px;
            }
            @media #{$xs}{
                top: 10px;
                right: 10px;
            }
        }
    }
}
.tp-about-5{
    &-area{
        @media #{$md,$xs}{
            padding-bottom: 30px;
        }
    }
    &-title{
        color: #F5F7F5;
        font-size: 70px;
        font-weight: 500;
        line-height: 1.1;
        letter-spacing: -2.8px;
        text-transform: uppercase;
        display: inline-block;
        @media #{$lg}{
            font-size: 55px;
        }
        @media #{$md}{
            font-size: 38px;
            & br{
                display: none;
            }
            & span{
                & svg{
                    width: 70px;
                }
            }
        }
        @media #{$xs}{
            font-size: 34px;
            & br{
                display: none;
            }
            & span{
                & svg{
                    width: 70px;
                }
            }
        }
        & .text-space{
            padding-left: 285px;
            @media #{$xs}{
                padding-left: 0;
            }
        }
    }
    &-subtitle{
        top: 14px;
        left: 0;
        position: absolute;
        font-size: 22px;
        font-weight: 400;
        line-height: 1;
        color: var(--tp-common-white);
        text-transform: lowercase;
        font-family: var(--tp-ff-body);
        letter-spacing: normal;
        @media #{$xs}{
            position: static;
            display: block;
            margin-bottom: 20px;
        }
    }
    &-category{
        padding-left: 100px;
         @media #{$lg,$md}{
            padding-left: 0;
        }
         @media #{$xs}{
            padding-left: 0;
            margin-bottom: 40px;
        }
        & span{
            font-size: 20px;
            font-weight: 400;
            line-height: 1;
            padding: 8px 17px;
            border-radius: 20px;
            display: table;
            color: var(--tp-common-white);
            border: 1px solid rgba(245, 247, 245, 0.20);
            margin-bottom: 10px;
            @media #{$xs}{
                font-size: 17px;
            }
            &:last-child{
                margin-bottom: 0;
            }
        }
    }
    &-text{
        padding-left: 70px;
        padding-right: 110px;
        @media #{$xxl}{
            padding-left: 0;
        }
        @media #{$xl}{
            padding-left: 0;
            padding-right: 60px;
        }
        @media #{$lg}{
            padding-left: 0;
            padding-right: 40px;
        }
        @media #{$md,$xs}{
            padding-left: 0;
            padding-right: 0px;
        }
        & p{
            font-size: 18px;
            font-weight: 400;
            line-height: 26px;
            color: rgba(245, 247, 245, 0.70);
        }
    }
}
.tp-mission{
    &-content{
        padding-left: 70px;
        @media #{$md,$xs}{
            padding-left: 0;
        }
        & p{
            font-size: 17px;
            font-weight: 400;
            line-height: 26px;
            color: rgba(245, 247, 245, 0.70);
            max-width: 375px;
            margin-bottom: 50px;
        }
    }
    &-thumb{
        height: 370px;
        overflow: hidden;
        @media #{$xs}{
            margin-bottom: 40px;
            & img{
                width: 100%;
            }
        }
    }
    &-right{
        &-thumb{
            position: absolute;
            bottom: -13%;
            right: 0;
            height: 260px;
            overflow: hidden;
            @media #{$lg}{
                bottom: -17%;
            }
            @media #{$md}{
                position: static;
                margin-top: 50px;
                text-align: center;
            }
        }
    }
}
.tp-about-6{
    &-title{
        font-size: 80px;
        font-weight: 400;
        line-height: 1;
        color: var(--tp-common-black);
        font-family: var(--tp-ff-gallery);
        @media #{$xs}{
            font-size: 45px;
        }
    }
    &-tab{
        &-btn{
            padding: 7px;
            background-color: #F5F5F5;
            display: inline-block;
            border-radius: 40px;
            margin-bottom: 90px;
            @media #{$xs}{
                padding: 20px;
                margin-bottom: 50px;
            }
            & ul{
                & li{
                    &:last-child{
                        & button{
                            margin-right: 0;
                        }
                    }
                    & button{
                        height: 40px;
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 14px;
                        padding: 0px 40px;
                        border-radius: 40px;
                        display: inline-block;
                        text-transform: uppercase;
                        color: var(--tp-common-black);
                        @media #{$md}{
                            padding: 0px 30px;
                        }
                        @media #{$xs}{
                            font-size: 14px;
                            padding: 0px 20px;
                        }
                        @media #{$sm}{
                            padding: 0px 18px;
                        }
                        &.active{
                            background-color: var(--tp-common-orange);   
                            color: var(--tp-common-white);                       
                        }
                    }
                }
            }
        }
    }
    &-pragraph-text{
        & p{
            font-size: 46px;
            font-weight: 400;
            line-height: 1.1;
            margin-bottom: 50px;
            text-align: left;
            color: var(--tp-common-black);
            font-family: var(--tp-ff-gallery);
            @media #{$xl,$lg,$md,$xs}{
                & br{
                    display: none;
                }
            }
            @media #{$md}{
                font-size: 40px;
            }
            @media #{$xs}{
                font-size: 30px;
            }
        }
    }
    &-personal-info{
        margin-bottom: 60px;
        & ul{
            & li{
                list-style: none;
                line-height: 0;
                margin-bottom: 15px;
                & span{
                    font-size: 18px;
                    font-weight: 400;
                    color: var(--tp-common-black);
                    & p{
                        color: #5D5D63;
                        min-width: 130px;
                        display: inline-block;
                        margin-bottom: 0;
                        @media #{$xs}{
                            min-width: 100px;
                        }
                    }
                    & i{
                        font-style: normal;
                        margin-right: 40px;
                    }
                }
            }
        }
    }
    &-btn-box{
        @media #{$xs}{
            margin-bottom: 40px;
        }
    }
}
.breadcurmb-site{
    & h6{
        color: var(--tp-common-white);
        transform: rotate(-90deg);
        position: absolute;
        top: 58.5%;
        left: 10px;
        &::before{
            content: '';
            height: 1px;
            width: 50px;
            display: inline-block;
            background-color: var(--tp-common-white);
            transform: translateY(-4px);
            margin-right: 12px;
        }
    }
}
.ab-inner-hero{
    &-scroll{
        position: absolute;
        right: 60px;
        top: 62%;
        @media #{$xs}{
            top: 61%;
        }
        & span{
            font-size: 18px;
            font-weight: 500;
            line-height: 1;
            letter-spacing: -0.36px;
            color: rgba(255, 255, 255, 0.70);
            & svg{
                margin-left: 20px;
                display: inline-block;
                color: var(--tp-common-white);
                animation: scroll-up-down 1s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite alternate;
                animation-delay: 0s;
                animation-delay: 0s;
                animation-delay: 0s;
                -webkit-animation-delay: 0.75s;
                animation-delay: 0.75s;
                margin-top: -25px;
            }
        }
    }
    &-bg{
        padding-top: 290px;
        padding-bottom: 110px;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        position: relative;
        z-index: 1;
        @media #{$xl}{
            padding-top: 210px;
        }
        @media #{$lg}{
            padding-top: 200px;
        }
        @media #{$md,$xs}{
            padding-top: 160px;
        }
        &::after{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            content: '';
            background: rgba(0, 0, 0, 0.40);
            z-index: -1;
        }
    }
    &-subtitle{
        font-size: 15px;
        font-weight: 600;
        line-height: 1.2;
        text-transform: uppercase;
        color: var(--tp-common-white);
        display: inline-block;
        position: relative;
        padding-left: 14px;
        margin-bottom: 20px;
        &::after{
            position: absolute;
            top: -3px;
            left: 0;
            width: 2px;
            height: 40px;
            content: '';
            background-color: var(--tp-common-white);
        }
    }
    &-title{
        font-size: 180px;
        font-weight: 500;
        line-height: .9;
        letter-spacing: -9px;
        color: var(--tp-common-white);
        transform: translateX(-12px);
        margin-bottom: 30px;
        @media #{$xl}{
            font-size: 160px;
        }
        @media #{$lg}{
            font-size: 130px;
        }
        @media #{$md}{
            font-size: 115px;
        }
        @media #{$xs}{
            font-size: 85px;
            transform: translateX(-3px);
        }
    }
    &-title-box{
        margin-bottom: 205px;
        & p{
            font-size: 22px;
            font-weight: 500;
            line-height: 34px;
            letter-spacing: -0.44px; 
            color: rgba(255, 255, 255, 0.70);
            @media #{$xs}{
                font-size: 20px;
            }
        }
    }
    &-content{
        & p{
            font-size: 40px;
            font-weight: 500;
            line-height: 1.1;
            letter-spacing: -0.8px;
            color: var(--tp-common-white);
            margin-bottom: 25px;
            @media #{$md}{
                font-size: 33px;
            }
            @media #{$xs}{
                font-size: 30px;
            }
        }
        & span{
            font-size: 18px;
            font-weight: 600;
            line-height: 1;
            letter-spacing: -0.36px;
            color: var(--tp-common-white);
            position: relative;
            margin-left: 20px;
            display: inline-block;
            &::before{
                position: absolute;
                top: 6px;
                left: -18px;
                height: 8px;
                width: 8px;
                content: '';
                border-radius: 50%;
                border: 1px solid var(--tp-common-white);
            }
            &::after{
                position: absolute;
                bottom: -2px;
                left: 0;
                height: 1px;
                width: 100%;
                content: '';
                background-color: var(--tp-common-white);
            }
        }
    }
}
.ab-about{
    &-mt{
        margin-top: -110px;
        @media #{$lg,$md,$xs}{
            margin-top: 0;
            margin-top: 100px;
        }
    }
    &-left-thumb{
        height: 690px;
        width: 600px;
        overflow: hidden;
        @media #{$xl}{
            width: 500px;
        }
        @media #{$lg}{
            height: 550px;
            width: 450px;
        }
        @media #{$md}{
            height: 550px;
            width: 330px;
        }
        @media #{$xs}{
            height: 400px;
            width: 100%;
        }
        & img{
            display: inline-block;
            @media #{$xs}{
                width: 100%;
            }
        }
    }
    &-right-thumb{
        & .inner-img{
            position: absolute;
            bottom: -44%;
            right: 0;
            @media #{$md}{
                position: static;
                margin-bottom: 30px;
            }
            @media #{$xs}{
                position: static;
                margin: 30px 0;
                width: 100%;
            }
        }
    }
    &-content{
        margin-bottom: 80px;
        & span{
            height: 60px;
            line-height: 60px;
            color: var(--tp-common-white);
            background-color: var(--tp-common-black-2);
            padding: 0px 20px;
            display: inline-block;
            font-size: 30px;
            font-weight: 600;
            position: absolute;
            top: -55px;
            left: 20px;
            transform: rotate(-24deg);
            & svg{
                transform: translateY(-5px) rotate(25deg);
                margin-left: -5px;
            }
        }
        & P{
            font-size: 54px;
            font-weight: 400;
            line-height: 1.2;
            letter-spacing: -1.08px;
            color: var(--tp-common-black-2);
            padding-right: 25px;
            @media #{$lg}{
                font-size: 50px;
            }
            @media #{$md}{
                font-size: 33px;
            }
            @media #{$xs}{
                font-size: 30px;
            }
        }
    }
    &-category{
        &-title{
            font-size: 22px;
            font-weight: 600;
            line-height: 1.3;
            letter-spacing: -0.88px;
            text-transform: uppercase;
            color: var(--tp-common-black-2);
            display: inline-block;
            & span{
                color: #141414;
                font-size: 18px;
                font-weight: 500;
            }
        }
        &-title-box{
            display: inline-block;
            padding-left: 100px;
            @media #{$md,$xs}{
                padding-left: 0;
            }
        }
        &-list{
            & ul{
                & li{
                    color: #5D5D63;
                    font-size: 18px;
                    font-weight: 500;
                    line-height: 1;
                    position: relative;
                    list-style-type: none;
                    padding-left: 18px;
                    margin-bottom: 18px;
                    &::after{
                        position: absolute;
                        top: 6px;
                        left: 0;
                        height: 6px;
                        width: 6px;
                        background-color: #5D5D63;
                        content: '';
                    }
                }
            }
            &.category-space-1{
                padding-left: 10px;
                @media #{$xs}{
                    padding-left: 0;
                }
            }
            &.category-space-2{
                padding-left: 45px;
                @media #{$xs}{
                    padding-left: 0;
                }
            }
        }
    }
    &-shape-1{
        position: absolute;
        bottom: -85px;
        right: 5%;
    }
}
.ab-award{
    &-style{
        & .tp-award-list-thumb-wrap {
            margin-top: 50px;
        }
    }
    &-title-sm{
        font-size: 18px;
        font-weight: 500;
        line-height: 1;
        color: var(--tp-common-black-2);
        transform: translateY(20px);
        & span{
            & svg{
                transform: translateY(-1px);
                margin-right: 5px;
                display: inline-block;
            }
        }
    }
}
.ab-inner{
    &-subtitle{
        font-size: 18px;
        font-weight: 500;
        line-height: 1;
        color: var(--tp-common-black-2);
        display: inline-block;
        & svg{
            transform: translateY(-2px);
            margin-right: 5px;
            display: inline-block;
        }
    }
    &-funfact-title{
        font-size: 54px;
        font-weight: 500;
        line-height: 1;
        letter-spacing: -1.08px;
        color: var(--tp-common-black-2);
    }
}
.ab-funfact{
    &-item{
        padding-bottom: 50px;
        border-bottom: 1px solid #D9D9D9;
        & span{
            font-size: 50px;
            font-weight: 400;
            line-height: 1;
            letter-spacing: -2px;
            color: var(--tp-common-black-2);
            margin-bottom: 10px;
            display: inline-block;
            & i{
                font-size: 100px;
                font-style: normal;
            }
        }
        & p{
            font-size: 16px;
            font-weight: 400;
            line-height: 1;
            margin-bottom: 0;
            color: var(--tp-common-black-2);
        }
    }
    &-title-box{
        @media #{$lg}{
            margin-bottom: 40px;
        }
    }
}
.ab-brand{
    &-title{
        font-size: 60px;
        font-weight: 500;
        line-height: 1;
        letter-spacing: -1.2px;
        color: var(--tp-common-white);
    }
    &-slide-wrap{
        -webkit-transition-timing-function: linear;
        transition-timing-function: linear;
    }
    &-content{
        & p{
            font-size: 28px;
            font-weight: 500;
            line-height: 1.3;
            color: var(--tp-common-white);
            margin-bottom: 45px;
        }
        & span{
            font-size: 18px;
            font-weight: 600;
            line-height: 1;
            letter-spacing: -0.36px;
            color: var(--tp-common-white);
            position: relative;
            margin-left: 20px;
            display: inline-block;
            &::before{
                position: absolute;
                top: 6px;
                left: -18px;
                height: 8px;
                width: 8px;
                content: '';
                border-radius: 50%;
                border: 1px solid rgba(255, 255, 255, 0.40);
            }
            &::after{
                position: absolute;
                bottom: -2px;
                left: 0;
                height: 1px;
                width: 100%;
                content: '';
                background-color: var(--tp-common-white);
            }
        }
    }
}
.ab-2-hero{
    &-ptb{
        padding-top: 200px;
        padding-bottom: 300px;
        @media #{$lg,$md}{
            padding-bottom: 100px;
        }
        @media #{$xs}{
            padding-top: 150px;
            padding-bottom: 0px;
        }
    }
    &-subtitle{
        font-size: 20px;
        font-weight: 400;
        line-height: 1;
        color: rgba(23, 23, 23, 0.50);
        margin-bottom: 15px;
        display: inline-block;
    }
    &-title{
        font-size: 160px;
        font-weight: 500;
        line-height: 1;
        letter-spacing: -6.4px;
        color: var(--tp-common-black-2);
        transform: translateX(-7px);
        margin-bottom: 32px;
        @media #{$lg,$xl}{
            font-size: 130px;
        }
        @media #{$md}{
            font-size: 140px;
        }
        @media #{$xs}{
            font-size: 80px;
        }
    }
    &-thumb-wrap{
        margin-top: -48px;
        padding-left: 70px;
        display: inline-block;
        @media #{$lg}{
            margin-top: 60px;
        }
        @media #{$md}{
            margin-top: 60px;
            padding-left: 0;
        }
        @media #{$xs}{
            margin-top: 60px;
            padding-left: 0;
            & img{
                margin-bottom: 60px;
            }
        }
        & .ab-2-hero-title{
            position: absolute;
            right: -78%;
            top: 9%;
            z-index: 1;
            @media #{$xl}{
                right: -70%;
            }
            @media #{$lg}{
                right: -60%;
            }
            @media #{$md}{
                right: -45%;
            }
            @media #{$xs}{
                position: static;
                & br{
                    display: none;
                }
            }
        }
    }
    &-shape{
        &-1{
            position: absolute;
            top: 20%;
            right: -55%;
            animation: animationglob 5s cubic-bezier(1, 0.99, 0.03, 0.01) infinite;
        }
        &-2{
            position: absolute;
            bottom: -65%;
            left: 10%;
        }
    }
    &-social{
        &-wrap{
            position: absolute;
            top: 45%;
            transform: translateY(-50%);
            left: 45px;
            @media #{$xl}{
                top: 65%;
            }
        }
        & a{
            display: table;
            color: #5D5D63;
            font-size: 20px;
            margin-bottom: 10px;
            &:last-child{
                margin-bottom: 0;
            }
        }
        &-text{
            & span{
                color: #5D5D63;
                font-size: 14px;
                font-weight: 600;
                line-height: 1;
                letter-spacing: 0.56px;
                position: relative;
                display: inline-block;
                transform: rotate(90deg);
                top: 65px;
                left: -64px;
                &::before{
                    content: '';
                    height: 1px;
                    width: 50px;
                    margin-right: 14px;
                    display: inline-block;
                    background-color: #5D5D63;
                    transform: translateY(-4px);
                }
            }
        }
    }
}
.ab-2-portfolio{
    &-thumb-wrap{
        padding: 0 5px;
    }
    &-thumb{
        & img{
            transition: .9s;
        }
        &:hover{
            & img{
                transform: scale(1.1) rotate(-2deg);
            }
        }
    }
}
.ab-2-about{
    &-title-style{
        & .ab-about-category-title span {
            font-size: 18px;
            font-weight: 500;
            line-height: 1;
            text-transform: capitalize;
            color: rgba(23, 23, 23, 0.50);
        }
    }
}
.ab-2-work{
    &-title-box{
        display: inline-block;
        @media #{$md,$xs}{
            margin-bottom: 60px;
        }
    }
    &-title{
        font-size: 60px;
        font-weight: 600;
        line-height: 1;
        letter-spacing: -2.4px;
        margin-bottom: 20px;
        color: var(--tp-common-black-2);
        @media #{$xs}{
            font-size: 55px;
        }
    }
    &-subtitle{
        font-size: 20px;
        font-weight: 500;
        line-height: 1;
        color: rgba(23, 23, 23, 0.50);
    }
    &-shape{
        position: absolute;
        bottom: -65%;
        right: 30%;
    }
}
.ab-2-work-item{
    margin-bottom: 50px;
    border-bottom: 1px solid rgba(23, 23, 23, 0.14);
    & .sv-service-space-wrap {
        padding-left: 0px;
    }
    & .sv-service-subtitle {
        color: #5D5D63;
    }
    & .sv-service-subtitle i::after {
        background: rgba(23, 23, 23, 0.14);
    }
    & .sv-service-title {
        font-size: 40px;
        font-weight: 600;
        line-height: 1;
        letter-spacing: -1.6px;
        color: var(--tp-common-black-2);
        @media #{$xs}{
            font-size: 30px;
        }
    }
    & .sv-service-text p {
        color: #5D5D63;
    }
}