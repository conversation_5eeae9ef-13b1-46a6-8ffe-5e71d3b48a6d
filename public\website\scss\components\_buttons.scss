@use '../utils' as *;

/*----------------------------------------*/
/*  2.3 Buttons
/*----------------------------------------*/

// black-btn
.tp-btn{
    &-black{
        height: 60px;
        line-height: 60px;
        border-radius: 40px;
        padding: 0 42px;
        font-weight: 500;
        font-size: 16px;
        letter-spacing: 0.03em;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
        transition-duration: 800ms;
        &::before{
            position: absolute;
            width: 200%;
            height: 200%;
            content: "";
            top: -200%;
            left: 50%;
            z-index: 1;
            border-radius: 50%;
            transition-duration: 800ms;
            transform: translateX(-50%);
            -webkit-transform: translateX(-50%);
            background: var(--tp-common-white);
            webkit-transition-duration: 800ms;
        }
        &-wrap{
            position: relative;
            z-index: 1;
            float: left;
            overflow: hidden;
            display: inline-block;
            & .text-1{
                position: relative;
                display: block;
                color: var(--tp-common-white);
                transition: all 0.3s ease;
                -o-transition: all 0.3s ease;
                -webkit-transition: all 0.3s ease;
            }
            & .text-2{
                position: absolute;
                top: 100%;
                display: block;
                color: var(--tp-common-black);
                -webkit-transition: all 0.3s ease;
                -o-transition: all 0.3s ease;
                transition: all 0.3s ease;
            }
        }
        @media #{$xs}{
            height: 50px;
            line-height: 50px;
            padding: 0px 30px;
        }
        &:hover{
            color: var(--tp-common-white);
            background-color: transparent;
            &::before{
                top: -40%;
            }
            & .tp-btn-black-wrap{
                & .text-1{
                    -webkit-transform: translateY(-150%);
                    -ms-transform: translateY(-150%);
                    transform: translateY(-150%);
                }
                & .text-2{
                    top: 50%;
                    -webkit-transform: translateY(-50%);
                    -ms-transform: translateY(-50%);
                    transform: translateY(-50%);
                }
            }
        }
    }
    &-black-animated{
        & span{
            font-size: 26px;
            font-weight: 700;
            border-radius: 100px;
            display: inline-block;
            color: var(--tp-common-white);
            background-color: var(--tp-common-black);
            font-family: var(--tp-ff-shoulders);
            position: relative;
            &.btn-1{
                padding: 0px 80px;
                height: 140px;
                line-height: 140px;
                transition: all 0.5s ease-in-out 0s;
            }
            &.btn-2{
                padding: 0px 36px;
                height: 140px;
                line-height: 140px;
                position: relative;
                left: -5px;
                transition: all 0.5s ease-in-out 0s;
            }
            &.btn-3{
                height: 60px;
                line-height: 60px;
                padding: 0px 43px;
                transform: rotate(-90deg);
                position: relative;
                left: -44px;
                transition: all 0.5s ease-in-out 0s;
            }
            &.btn-expand{
                position: absolute;
                left: 0;
                height: 100%;
                width: 172px;
                border-radius: 100px;
                background-color: var(--tp-common-black);
                transition: all 0.5s ease-in-out 0s;
            }
        }
        &:hover{
            & span{
                &.btn-expand{
                    width: 295px;
                } 
                &.btn-2{
                    left: -65px;
                    padding: 0;
                }
                &.btn-3{
                    left: -53px;
                    padding: 0;
                    transform: rotate(0deg);
                }
            }
        }
    }
    &-black-2{
        height: 60px;
        line-height: 55px;
        border-radius: 40px;
        padding: 0 18px 0 24px;
        font-weight: 500;
        font-size: 18px;
        letter-spacing: 0.03em;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
        border: 2px solid transparent;
        @media #{$xs}{
            height: 50px;
            line-height: 43px;
            padding: 0px 30px;
        }
        &:hover{
            background-color: transparent;
            color: var(--tp-common-black);
            border-color: var(--tp-common-black);
            & span{
                & .svg-icon{
                    color: var(--tp-common-white);
                }
                & .svg-bg{
                    animation: rotate2 10s linear infinite;
                }
            }
        }
        & span{
            margin-left: 12px;
            & .svg-icon{
                position: absolute;
                top: 7px;
                left: -2px;
                right: 0;
                z-index: 2;
                margin: 0 auto;
                transition: 0.3s;
                color: var(--tp-common-black);
            }
        }
    }
    &-black-square{
        height: 66px;
        line-height: 66px;
        padding: 0 35px;
        font-weight: 500;
        letter-spacing: -0.18px;
        font-size: 20px;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all 0.3s;
        border: 1.5px solid rgba(18, 18, 18, 0.2);
        color: var(--tp-common-black);
        & span{
            margin-left: 10px;
        }
        &:hover{
            color: var(--tp-common-white);
            background-color: var(--tp-common-black);
            border-color: var(--tp-common-black);
        }
    }
    &-black-sm{
        height: 44px;
        line-height: 44px;
        border-radius: 4px;
        padding: 0 27px;
        font-weight: 500;
        font-size: 16px;
        letter-spacing: 0.03em;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        border: 2px solid transparent;
        color: var(--tp-common-white);
        background-color: var(--tp-common-black);
        &:hover{
            color: var(--tp-common-black);
            border-color: var(--tp-common-black);
            background-color: transparent;
        }
    }
    &-black-md{
        height: 50px;
        line-height: 46px;
        padding: 0 27px;
        font-weight: 500;
        font-size: 16px;
        letter-spacing: 0.03em;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
        border: 2px solid transparent;
        & span{
            margin-left: 10px;
        }
        &.white-bg{
            color: var(--tp-common-black-2);
            &:hover{
                color: var(--tp-common-white);
                border-color: var(--tp-common-white);
                background-color: transparent;
            }
        }
        &:hover{
            color: var(--tp-common-black);
            border-color: var(--tp-common-black);
            background-color: transparent;
        }
    }
    &-white{
        height: 38px;
        line-height: 33px;
        border-radius: 100px;
        padding: 0 20px;
        font-weight: 600;
        font-size: 16px;
        letter-spacing: 0.03em;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        background-color: var(--tp-common-white);
        color: var(--tp-common-black); 
        & span{
            padding-left: 3px;
            color: var(--tp-common-black);
            transform: translateY(-1px);
            display: inline-block;
            transition: .3s;
        }
        &:hover{
            background-color: var(--tp-common-black);
            color: var(--tp-common-white);
            & span{
                color: var(--tp-common-white);
            }
        }
        &.background-black{
            background-color: var(--tp-common-black);
            color: var(--tp-common-white);
            border: 2px solid transparent;
            transition: .3s;
            & span{
                color: var(--tp-common-white);
            }
            &:hover{
                background-color: transparent;
                border-color: var(--tp-common-black);
                color: var(--tp-common-black);
                & span{
                    color: var(--tp-common-black);
                }
            }
        }
    }
    &-white-lg{
        height: 50px;
        line-height: 50px;
        padding: 0 30px;
        font-size: 16px;
        z-index: 9;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 600;
        text-transform: uppercase;
        background-color: var(--tp-common-white);
        color: var(--tp-common-black); 
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        transition: .4s;
        &:hover{
            transform: scale(1.2);
            color: var(--tp-common-black);
        }
        
    }
    &-white-sm{
        z-index: 9;
        height: 30px;
        line-height: 30px;
        padding: 0 17px;
        font-size: 15px;
        font-weight: 600;
        border-radius: 4px;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        white-space: nowrap;
        color: var(--tp-common-black);
        background-color: var(--tp-common-white);
        & svg{
            margin-left: 5px;
            margin-top: -1px;
            width: 18px;
        }
        &:hover{
            & svg{
                -webkit-animation: iconMove ease-out .35s;
                animation: iconMove ease-out .35s;
            }
        }
        &.border-style{
          height: 40px;
          line-height: 37px;
          font-size: 17px;
          background-color: transparent;
          color: var(--tp-common-white); 
          border: 1px solid #fff; 
          border-radius: 0;
          &:hover{
            background-color: #fff;
            color: var(--tp-common-black);
          }
        }
    }
    &-white-shape{
        font-size: 16px;
        font-weight: 600;
        line-height: 1;
        display: inline-block;
        padding: 32px 52px;
        text-align: center;
        transition: all 0.3s;
        border-radius: 100%;
        border: 2px solid transparent;
        color: var(--tp-common-black);
        background-color: var(--tp-common-white-solid);
        &:hover{
            background-color: transparent;
            color: var(--tp-common-white);
            border-color: var(--tp-common-white-solid);
        }
    }
    &-zikzak{  
        font-size: 18px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: -0.18px;
        color: var(--tp-common-black);
        z-index: 1;
        & .zikzak-content{
            position: absolute;
            top: -17px;
            left: 43px;
            padding-left: 0;
            & svg{
                display: block;
                margin-top: 7px;
                transition: .7s;
            }
        } 
        & svg.anim{
            position: relative;
            z-index: -1;
        }
        &:hover{
            color: var(--tp-common-black);
            & svg.anim{
                animation: rotate2 10s linear infinite;
            }
            & .zikzak-content{
                & svg{
                    transform: translateX(60px);
                }
            }
        }
        &.zikzak-inner{
            color: var(--tp-common-white); 
            & .zikzak-content {
                left: 37px;
            }
            &:hover{
                & .zikzak-content{
                    & svg{
                        transform: translateX(40px);
                    }
                }
            }
        }
    }
    &-zikzak-sm{  
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: -0.18px;
        color: var(--tp-common-black);
        text-align: left;
        transition: .3s ease-in-out;
        & .zikzak-content{
            position: absolute;
            top: -20px;
            left: 43px;
            z-index: 2;
            & svg{
                display: block;
                margin-top: 7px;
                transition: .7s;
            }
        }
        & span{
            & .svg-bg{
                color: var(--tp-common-white-solid);
                transition: .3s ease-in-out;
            }
        }
        &:hover{
            & .zikzak-content{
                color: var(--tp-common-white-solid);
                & svg{
                    transform: translateX(30px);
                }
            }
            & span{
                & .svg-bg{
                    color: var(--tp-common-black);
                    animation: rotate2 10s linear infinite;
                }
            } 
        } 
    }
    &-project-sm{
        height: 32px;
        line-height: 32px;
        border-radius: 20px;
        padding: 0 22px;
        overflow: hidden;
        display: inline-block;
        position: relative;
        transition: all .3s;
        text-align: center;
        font-size: 13px;
        font-weight: 600;
        text-transform: uppercase;
        color: rgba(255, 255, 255, 0.90);
        border: 1px solid rgba(255, 255, 255, 0.12);
        &:hover{
            background-color: var(--tp-common-white);
            border-color: var(--tp-common-white);
            color: var(--tp-common-black);
        }
    }
    &-animation{
        border-top: 1px solid rgba(18, 18, 18, 0.10);
        border-bottom: 1px solid rgba(18, 18, 18, 0.10);
        height: 45px;
        line-height: 45px;
        padding: 0 17px;
        display: inline-block;
        white-space: nowrap;
        animation: scrollText 20s infinite linear;
        &:hover{
            animation-play-state: paused;
        }
        & span{
            font-weight: 500;
            font-size: 16px;
            z-index: 9;
            overflow: hidden;
            display: inline-block;
            text-transform: uppercase;
            position: relative;
            transition: all .3s;
            color: var(--tp-common-black);
            &::before{
                height: 5px;
                width: 5px;
                background-color: var(--tp-common-black);
                display: inline-block;
                content: '';
                margin-right: 15px;
                margin-left: 15px;
                border-radius: 50%;
                transform: translateY(-3px);
            }
        }
    }
    &-circle{
        font-weight: 500;
        font-size: 18px;
        line-height: 1.22;
        letter-spacing: -0.01em;
        color: var(--tp-common-white);
        border: 1px solid rgba(255, 255, 255, 0.3);
        display: inline-block;
        width: 149px;
        height: 149px;
        border-radius: 50%;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        &.style-2{
            border-color: rgba(25, 25, 26, 0.14);
            color: var(--tp-common-black);
        }
        &-icon{
            transform: translateY(10px);
            margin-left: 8px;
        }
        & .tp-btn-circle-dot{
            position: absolute;
            bottom: 0;
            left: 32px;
            width: 20px;
            height: 20px;
            @include transition(all ,0.6s);
            @include rounded-btn(20px, 20px, 20px);
            background-color: var(--tp-common-white);
            @include transform(translate(-50%, -50%));
            z-index: -1;
        }
        &:hover{
            border: 1px solid transparent;
            & .tp-btn-circle-dot{
                width: 420px;
                height: 420px;
            }
            & span{
                color: var(--tp-common-black);
            }
        }
        &-2{
            font-weight: 500;
            font-size: 18px;
            line-height: 1.22;
            letter-spacing: -0.01em;
            color: var(--tp-common-white);
            border: 1px solid rgba(255, 255, 255, 0.50);
            display: inline-block;
            width: 149px;
            height: 149px;
            border-radius: 50%;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
            & span{
                text-align: center;
            }
            & .tp-btn-circle-dot{
                position: absolute;
                bottom: 0;
                left: 32px;
                width: 20px;
                height: 20px;
                @include transition(all ,0.6s);
                @include rounded-btn(20px, 20px, 20px);
                background-color: var(--tp-common-white);
                @include transform(translate(-50%, -50%));
                z-index: -1;
            }
            &:hover{
                border: 1px solid transparent;
                & .tp-btn-circle-dot{
                    width: 420px;
                    height: 420px;
                }
                & span{
                    color: var(--tp-common-black);
                }
            }
        }
    }
    &-orange{
        height: 50px;
        line-height: 50px;
        padding: 0 43px;
        font-size: 16px;
        font-size: 16px;
        font-weight: 600;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        color: var(--tp-common-white);
        background-color: var(--tp-common-orange);
        z-index: 9;
        &:hover{
            color: var(--tp-common-white);
        }
    }
}

// border-btn
.tp-btn{
    &-border{
        height: 80px;
        line-height: 80px;
        border-radius: 40px;
        padding: 0 70px;
        font-weight: 500;
        font-size: 20px;
        letter-spacing: 0.03em;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        color: var(--tp-common-black);
        border: 1px solid var(--tp-border-1);
        transition-duration: 800ms;
        @media #{$md,$xs}{
            height: 60px;
            line-height: 60px;
            border-radius: 40px;
            padding: 0 40px;
        }
        &::before{
            position: absolute;
            width: 200%;
            height: 200%;
            content: "";
            top: -200%;
            left: 50%;
            z-index: 1;
            border-radius: 50%;
            transition-duration: 800ms;
            transform: translateX(-50%);
            -webkit-transform: translateX(-50%);
            background: var(--tp-common-black);
            webkit-transition-duration: 800ms;
        }
        &-wrap{
            position: relative;
            z-index: 1;
            float: left;
            overflow: hidden;
            display: inline-block;
            & .text-1{
                position: relative;
                display: block;
                color: var(--tp-common-black);
                transition: all 0.3s ease;
                -o-transition: all 0.3s ease;
                -webkit-transition: all 0.3s ease;
            }
            & .text-2{
                position: absolute;
                top: 30%;
                display: block;
                color: var(--tp-common-white);
                -webkit-transition: all 0.3s ease;
                -o-transition: all 0.3s ease;
                transition: all 0.3s ease;
                opacity: 0;
                visibility: hidden;
            }
        }
        @media #{$xs}{
            height: 60px;
            line-height: 60px;
            padding: 0px 50px;
        }
        &:hover{
            border-color: var(--tp-common-black);
            &::before{
                top: -40%;
            }
            & .tp-btn-border-wrap{
                & .text-1{
                    -webkit-transform: translateY(-150%);
                    -ms-transform: translateY(-150%);
                    transform: translateY(-150%);
                }
                & .text-2{
                    top: 50%;
                    -webkit-transform: translateY(-50%);
                    -ms-transform: translateY(-50%);
                    transform: translateY(-50%);
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
    }
    &-border-lg{
        height: 60px;
        line-height: 58px;
        border-radius: 40px;
        padding: 0 30px;
        font-weight: 500;
        font-size: 16px;
        letter-spacing: 0.03em;
        z-index: 9;
        overflow: hidden;
        display: inline-block;
        text-transform: capitalize;
        position: relative;
        transition: all .3s;
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
        border: 2px solid transparent;
        @media #{$xs}{
            height: 50px;
            line-height: 50px;
            padding: 0px 30px;
        }
        &:hover{
            color: var(--tp-common-black);
            border-color: var(--tp-common-black);
            background-color: transparent;
        }
        & span{
            margin-left: 12px;

            & .svg-icon{
                position: absolute;
                top: 7px;
                left: -3px;
                right: 0;
                z-index: 2;
                margin: 0 auto;
            }
        }
    }
    &-border-sm{
        height: 44px;
        line-height: 42px;
        border-radius: 100px;
        padding: 0 30px;
        z-index: 9;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: -0.16px;
        overflow: hidden;
        position: relative;
        display: inline-block;
        text-transform: uppercase;
        color: var(--tp-common-white);
        border: 1px solid var(--tp-common-white);
        &:hover{
            color: var(--tp-common-black);
            background-color: var(--tp-common-white);
            border-color: var(--tp-common-white);
        }  
    }
}

.tp-shop-btn{
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    height: 50px;
    line-height: 50px;
    padding: 0px 45px;
    display: inline-block;
    text-transform: uppercase;
    color: var(--tp-common-white);
    font-family: var(--tp-ff-marcellus);
    border: 1px solid rgba(255, 255, 255, 0.20);
    &:hover{
        background-color: var(--tp-common-white);
        border-color: var(--tp-common-white);
        color: var(--tp-common-black);
    }
    &.border-style{
        border: 1px solid rgba(30, 30, 30, 0.2);
        color: var(--tp-common-black);
        &:hover{
            background-color: var(--tp-common-black);
            border-color: var(--tp-common-black);
            color: var(--tp-common-white);
        }
    }
}

.tp-btn{
    &-shop-category{
        font-size: 16px;
        font-weight: 400;
        height: 46px;
        line-height: 46px;
        padding: 0px 30px;
        transition: .3s;
        display: inline-block;
        text-align: center;
        text-transform: uppercase;
        color: var(--tp-common-black);
        font-family: var(--tp-ff-marcellus);
        background-color: var(--tp-common-white-solid);
        &:hover{
            background-color: var(--tp-common-black);
            color: var(--tp-common-white-solid);
        }
        &.black-bg{
            color: var(--tp-common-white);
        }
    }
}

.tp-filter-btn {
	font-weight: 500;
	font-size: 14px;
	line-height: 1;
	color: var(--tp-common-white);
	background-color: var(--tp-common-black);
	display: inline-block;
	padding: 13px 35px 9px;
	border: 1px solid var(--tp-common-black);
    @media #{$lg}{
        padding: 13px 32px 9px;
    }
}

.tp-btn-subscribe{
    font-size: 16px;
    font-weight: 400;
    height: 54px;
    line-height: 54px;
    padding: 0px 27px;
    transition: .3s;
    display: inline-block;
    text-align: center;
    text-transform: uppercase;
    color: var(--tp-common-black);
    font-family: var(--tp-ff-marcellus);
    background-color: #fff; 
}

.tp-btn-cart{
    font-size: 16px;
    font-weight: 500;
    height: 50px;
    line-height: 50px;
    padding: 0px 65px;
    transition: .3s;
    display: inline-block;
    text-align: center;
    text-transform: capitalize;
    color: var(--tp-common-white);
    font-family: var(--tp-ff-marcellus);
    background-color: var(--tp-common-black); 
    flex: 0 0 auto;
    border: 2px solid transparent;
    &:hover{
        background-color: transparent;
        border-color: var(--tp-common-black);
        color: var(--tp-common-black);
    }
    @media #{$xxxl}{
        padding: 0px 35px;
    }
    @media #{$xxl}{
        font-size: 14px;
        padding: 0px 15px;
    }
    @media #{$xl}{
        padding: 0px 20px;
    }
    & span{
        margin-right: 10px;
    }
    &.sm{
        height: 50px;
        line-height: 45px;
        padding: 0px 30px;
    }
}

.tp-btn-submit{
    font-size: 17px;
    font-weight: 400;
    height: 50px;
    line-height: 48px;
    padding: 0px 35px;
    transition: .3s;
    display: inline-block;
    text-align: center;
    color: var(--tp-common-white);
    font-family: var(--tp-ff-marcellus);
    background-color: var(--tp-common-black); 
    flex: 0 0 auto;
    & span{
        margin-right: 10px;
    }
}

.tp-btn-wishlist{
    font-weight: 400;
    height: 60px;
    width: 60px;
    line-height: 60px;
    font-size: 20px;
    transition: .3s;
    display: inline-block;
    text-align: center;
    text-transform: uppercase;
    color: var(--tp-common-black);
    border: 1px solid rgba(25, 25, 26, 0.12);
    &:hover{
        background-color: var(--tp-common-black);
        color: var(--tp-common-white);
        border-color: var(--tp-common-black);
    }
}
  