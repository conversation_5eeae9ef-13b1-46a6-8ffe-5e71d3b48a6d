<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class userRoleCheck
{
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login')->with('error', 'Please log in to access this page.');
        }
        // Convert the roles string into an array
       

        if (!in_array($user->role->role_key, $roles)) {
           
        // Check if the request is an API request
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized access. You do not have permission to perform this action.'
            ], 401);
        }
        return redirect()->route('dashboard.home')->with('error', 'You do not have permission to access this page.');
        }


        return $next($request);
    }
}
