<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Countries extends Model
{
    use HasFactory;
   // 🔽 Add this line to explicitly set the table name
    protected $table = 'countries';
    
    protected $fillable = [
        'id',
        'name',
        'created_at',
        'updated_at',
    ];


    public function states()
    {
        return $this->hasMany(States::class, 'country_id');
    }
 
}