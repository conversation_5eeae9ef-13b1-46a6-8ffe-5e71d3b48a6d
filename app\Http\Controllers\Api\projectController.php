<?php

namespace App\Http\Controllers\Api;
use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Models\Projects;
use App\Models\User;
use App\Models\Franchisees;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;

class projectController extends Controller
{




    public function storeProject(Request $request)
    {
        $user = Auth::user();

        // Validate the request
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'postcode' => 'required|string|max:255',
            'project_value' => 'required|numeric',
            'payment_type' => 'required|string|max:255',
           
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }


        // Create the association record
        $project = Projects::create([
            'franchisee_id' => $user->franchisee->id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'project_postcode' => $request->postcode,
            'project_value' => $request->project_value,
            'payment_type' => $request->payment_type,
            'notes' => $request->project_notes,
            'attachment' => $request->attachment,
            'start_date' => $request->project_start_date,
           
            'project_status' => 0,

        ]);


        return response()->json(['message' => 'project created successfully!', "status" => "success"], 200);
    }

    public function updateProject(Request $request, $id)
    {
        // Find the project by ID
        $project = Projects::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'postcode' => 'required|string|max:255',
            'project_value' => 'required|numeric',
            'payment_type' => 'required|string|max:255',
           
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Update association data
        $project->first_name = $request->first_name;
        $project->last_name = $request->last_name;
        $project->project_postcode = $request->postcode;
      
         $project->project_value = $request->project_value;
        $project->payment_type = $request->payment_type;
        $project->notes = $request->project_notes;
        $project->attachment = $request->attachment;
         $project->start_date = $request->project_start_date;
          

        $project->save();

        return response()->json(['message' => 'Project updated successfully!', "status" => "success"], 200);
    }
    public function ProjectStatus($id)
    {
        // Find the project by ID
        $project = Projects::findOrFail($id);
        $project->project_status = !$project->project_status;
          $project->end_date = Carbon::now()->toDateString(); // format: Y-m-d
        $project->save();

        return response()->json(['message' => 'project status updated successfully!', "status" => "success"], 200);
    }

    public function deleteProject($id)
    {
        // Find the project by ID
        $project = Projects::findOrFail($id);
        $project->delete();

        return response()->json(['message' => 'project deleted successfully!', "status" => "success"], 200);
    }
}
