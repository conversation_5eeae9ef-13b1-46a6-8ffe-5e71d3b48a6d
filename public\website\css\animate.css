@-webkit-keyframes fadeIn {
	0% {
	  opacity: 0;
	}
  
	100% {
	  opacity: 1;
	}
  }
  
  @keyframes fadeIn {
	0% {
	  opacity: 0;
	}
  
	100% {
	  opacity: 1;
	}
  }
  
  .fadeIn {
	-webkit-animation-name: fadeIn;
	animation-name: fadeIn;
  }
  
  @-webkit-keyframes fadeInDown {
	0% {
	  opacity: 0;
	  -webkit-transform: translateY(-20px);
	  transform: translateY(-20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateY(0);
	  transform: translateY(0);
	}
  }
  
  @keyframes fadeInDown {
	0% {
	  opacity: 0;
	  -webkit-transform: translateY(-20px);
	  -ms-transform: translateY(-20px);
	  transform: translateY(-20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateY(0);
	  -ms-transform: translateY(0);
	  transform: translateY(0);
	}
  }
  
  .fadeInDown {
	-webkit-animation-name: fadeInDown;
	animation-name: fadeInDown;
  }


  
@-webkit-keyframes fadeInLeft {
	0% {
	  opacity: 0;
	  -webkit-transform: translateX(-20px);
	  transform: translateX(-20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateX(0);
	  transform: translateX(0);
	}
  }
  
  @keyframes fadeInLeft {
	0% {
	  opacity: 0;
	  -webkit-transform: translateX(-20px);
	  -ms-transform: translateX(-20px);
	  transform: translateX(-20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateX(0);
	  -ms-transform: translateX(0);
	  transform: translateX(0);
	}
  }
  
  .fadeInLeft {
	-webkit-animation-name: fadeInLeft;
	animation-name: fadeInLeft;
  }
  
@-webkit-keyframes fadeInRight {
	0% {
	  opacity: 0;
	  -webkit-transform: translateX(20px);
	  transform: translateX(20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateX(0);
	  transform: translateX(0);
	}
  }
  
  @keyframes fadeInRight {
	0% {
	  opacity: 0;
	  -webkit-transform: translateX(20px);
	  -ms-transform: translateX(20px);
	  transform: translateX(20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateX(0);
	  -ms-transform: translateX(0);
	  transform: translateX(0);
	}
  }
  
  .fadeInRight {
	-webkit-animation-name: fadeInRight;
	animation-name: fadeInRight;
  }
  @-webkit-keyframes fadeInUp {
	0% {
	  opacity: 0;
	  -webkit-transform: translateY(20px);
	  transform: translateY(20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateY(0);
	  transform: translateY(0);
	}
  }
  
  @keyframes fadeInUp {
	0% {
	  opacity: 0;
	  -webkit-transform: translateY(20px);
	  -ms-transform: translateY(20px);
	  transform: translateY(20px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateY(0);
	  -ms-transform: translateY(0);
	  transform: translateY(0);
	}
  }
  
  .fadeInUp {
	-webkit-animation-name: fadeInUp;
	animation-name: fadeInUp;
  }



  @keyframes tp-title-right {
	0% {
	  opacity: 0;
	  -webkit-transform: translateX(-100px);
	  -ms-transform: translateX(-100px);
	  transform: translateX(-100px);
	}
  
	100% {
	  opacity: 1;
	  -webkit-transform: translateX(0);
	  -ms-transform: translateX(0);
	  transform: translateX(0);
	}
  }
  
  .fadeInUp {
	-webkit-animation-name: tp-title-right;
	animation-name: tp-title-right;
  }