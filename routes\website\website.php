<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Controllers\website\websiteController;


// Route::get('/comming-soon', function () {
//     return view('website.comingSoon'); 
// });

Route::get('/', [websiteController::class, 'HomeView']);
Route::get('/home', [websiteController::class, 'HomeView']);

Route::get('/about', [websiteController::class, 'AboutView']);

Route::get('/404', function () {
    return view('website.404'); 
});

Route::get('/contact-us', function () {
    return view('website.contact'); 
});


Route::get('/blog', [websiteController::class, 'blogView']);

Route::get('/blog/{slug}', [websiteController::class, 'singleBlogView'])->name('blog.detail');

// services routes

Route::get('/services', function () {
    return view('website.service');
});

// Individual service pages
Route::get('/airline-tickets-service', function () {
    return view('website.airlineTickets');
});

Route::get('/hajj-umrah-service', function () {
    return view('website.hajjUmrah');
});

Route::get('/hotel-reservations-service', function () {
    return view('website.hotelReservations');
});

Route::get('/limousine-services', function () {
    return view('website.limousineServices');
});
