<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Controllers\website\websiteController;


// Route::get('/comming-soon', function () {
//     return view('website.comingSoon'); 
// });

Route::get('/', [websiteController::class, 'HomeView']);
Route::get('/home', [websiteController::class, 'HomeView']);

Route::get('/about', [websiteController::class, 'AboutView']);

Route::get('/404', function () {
    return view('website.404'); 
});

Route::get('/contact-us', function () {
    return view('website.contact'); 
});


Route::get('/blog', [websiteController::class, 'blogView']);

Route::get('/blog/{slug}', [websiteController::class, 'singleBlogView'])->name('blog.detail');

// services routes

Route::get('/services', function () {
    return view('website.service');
});

// Individual service pages

// Travel & Tourism Services
Route::get('/airline-tickets-service', function () {
    return view('website.airlineTickets');
});

Route::get('/tourism-packages-service', function () {
    return view('website.tourismPackages');
});

// Business & Corporate Services
Route::get('/company-formation-service', function () {
    return view('website.companyFormation');
});

Route::get('/cr-works-service', function () {
    return view('website.crWorks');
});

Route::get('/local-sponsors-service', function () {
    return view('website.localSponsors');
});

Route::get('/sponsor-arrangement-service', function () {
    return view('website.sponsorArrangement');
});

Route::get('/business-development-service', function () {
    return view('website.businessDevelopment');
});

Route::get('/pro-services', function () {
    return view('website.proServices');
});

// Financial & Legal Services
Route::get('/salary-transfer-assistance-service', function () {
    return view('website.salaryTransferAssistance');
});

Route::get('/tax-auditing-service', function () {
    return view('website.taxAuditing');
});

Route::get('/business-valuation-service', function () {
    return view('website.businessValuation');
});

// Government & Regulatory Services
Route::get('/hukoomi-services', function () {
    return view('website.hukoomServices');
});

Route::get('/baladiya-services', function () {
    return view('website.baladiyaServices');
});

Route::get('/attestation-service', function () {
    return view('website.attestationService');
});

Route::get('/documentation-support-service', function () {
    return view('website.documentationSupport');
});

Route::get('/typing-letters-contracts-service', function () {
    return view('website.typingLettersContracts');
});

Route::get('/translation-services', function () {
    return view('website.translationServices');
});

// Legacy routes (keeping for backward compatibility)
Route::get('/hajj-umrah-service', function () {
    return view('website.hajjUmrah');
});

Route::get('/hotel-reservations-service', function () {
    return view('website.hotelReservations');
});

Route::get('/limousine-services', function () {
    return view('website.limousineServices');
});
