<?php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SingleLeadMail extends Mailable
{
    use Queueable, SerializesModels;

    public $name;
    public $subject;
    public $bodyMessage;

    /**
     * Create a new message instance.
     */
    public function __construct($name, $subject, $bodyMessage)
    {
        $this->name = $name;
        $this->subject = $subject;
        $this->bodyMessage = $bodyMessage;
    }
 
    /**
     * Build the Message.
     */
    public function build()
    {
        return $this->subject($this->subject)
            ->view('emails.singleLeadMail')
            ->with([
                'name' => $this->name,
                'bodyMessage' => $this->bodyMessage,      
            ]);
    }
}
